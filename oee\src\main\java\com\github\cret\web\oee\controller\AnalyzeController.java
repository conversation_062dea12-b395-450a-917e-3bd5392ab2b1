package com.github.cret.web.oee.controller;

import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.List;

import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.alibaba.excel.EasyExcel;
import com.github.cret.web.oee.document.Workshop;
import com.github.cret.web.oee.domain.analyze.AlarmInfo;
import com.github.cret.web.oee.domain.analyze.AnalyzeQuery;
import com.github.cret.web.oee.domain.analyze.AnalyzeResult;
import com.github.cret.web.oee.domain.analyze.HourlyOutputList;
import com.github.cret.web.oee.domain.analyze.OeeResult;
import com.github.cret.web.oee.service.AnalyzeService;
import com.github.cret.web.oee.service.WorkshopService;

import jakarta.servlet.http.HttpServletResponse;

/**
 * 分析控制器
 */
@RestController
@RequestMapping("/analyze")
public class AnalyzeController {

	private final AnalyzeService analyzeService;

	private final WorkshopService workshopService;

	public AnalyzeController(AnalyzeService analyzeService, WorkshopService workshopService) {
		this.analyzeService = analyzeService;
		this.workshopService = workshopService;
	}

	/**
	 * 获取线体OEE等指标
	 * @param query 包含线体编码、开始时间和结束时间的查询条件
	 * @return OEE等指标结果
	 */
	@PostMapping("/line-oee")
	public AnalyzeResult getLineOee(@RequestBody AnalyzeQuery query) {
		return analyzeService.getLineOee(query);
	}

	/**
	 * 获取线体每小时产出数据
	 * @param query 包含线体编码、开始时间和结束时间的查询条件
	 * @return 每小时产出数据列表
	 */
	@PostMapping("/hourly-output")
	public HourlyOutputList getHourlyOutput(@RequestBody AnalyzeQuery query) {
		return analyzeService.getHourlyOutput(query);
	}

	/**
	 * 获取Top5异常统计数据
	 * @param query 包含线体编码、开始时间和结束时间的查询条件
	 * @return Top5异常统计列表
	 */
	@PostMapping("/top-alarms")
	public List<AlarmInfo> getTopAlarms(@RequestBody AnalyzeQuery query) {
		return analyzeService.getTopAlarms(query);
	}

	/**
	 * 获取服务器当前时间（上海时区 UTC+8）
	 * @return 服务器时间戳（毫秒）
	 */
	@PostMapping("/server-time")
	public Long getServerTime() {
		ZoneId zoneId = ZoneId.of("Asia/Shanghai");
		LocalDateTime dateTime = LocalDateTime.now(zoneId);
		return dateTime.atZone(zoneId).toInstant().toEpochMilli();
	}

	/**
	 * 月度oee指标查询
	 * @param workShopId
	 * @param query
	 * @return
	 */
	@PostMapping("/workshop-monthly-oee/{workShopId}")
	public List<OeeResult> getWorkshopMonthlyOee(@PathVariable String workShopId, @RequestBody AnalyzeQuery query) {
		return analyzeService.getWorkshopMonthlyOee(workShopId, query);
	}

	/**
	 * @param response
	 * @param param
	 * @throws IOException
	 */
	@PostMapping("/export/{workShopId}")
	public void exportExcel(HttpServletResponse response, @PathVariable String workShopId,
			@RequestBody AnalyzeQuery query) throws IOException {
		response.setContentType("application/vnd.ms-excel");
		response.setCharacterEncoding("utf-8");
		String fileName = URLEncoder.encode(workShopId + "车间数据", StandardCharsets.UTF_8);
		response.setHeader("Content-disposition", "attachment;filename=" + fileName + ".xlsx");

		List<OeeResult> oeeResults = analyzeService.getWorkshopMonthlyOee(workShopId, query);
		EasyExcel.write(response.getOutputStream(), OeeResult.class).sheet("oee指标").doWrite(oeeResults);
	}

	/**
	 * @param response
	 * @param param
	 * @throws IOException
	 */
	@PostMapping("/export/daily/{workShopId}")
	public void exportDailyExcel(HttpServletResponse response, @PathVariable String workShopId,
			@RequestBody AnalyzeQuery query) throws IOException {
		Workshop workshop = workshopService.findByCode(workShopId);
		response.setContentType("application/vnd.ms-excel");
		response.setCharacterEncoding("utf-8");
		String fileName = URLEncoder.encode(workshop.getName() + "车间数据", StandardCharsets.UTF_8);
		response.setHeader("Content-disposition", "attachment;filename=" + fileName + ".xlsx");

		List<OeeResult> oeeResults = analyzeService.getWorkshopDailyOee(workShopId, query);
		EasyExcel.write(response.getOutputStream(), OeeResult.class).sheet("oee指标").doWrite(oeeResults);
	}

}
