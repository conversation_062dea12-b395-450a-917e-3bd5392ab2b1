# URL参数加密配置示例
# 复制此文件为 application.properties 或合并到现有配置文件中

# 反馈系统URL配置
feedback.handle-base-url=http://localhost:3333/feedback/handle
feedback.dashboard-base-url=http://************:3000/dashboard
feedback.default-dashboard-path=SMT1-1

# URL参数加密配置
# 是否启用URL参数加密
feedback.enable-url-encryption=true

# URL参数加密密钥（Base64编码的256位AES密钥）
# 注意：在生产环境中应该使用环境变量或安全的配置管理系统来设置此密钥
# 可以使用 UrlEncryptionUtil.generateKey() 方法生成新的密钥
# 以下是一个有效的32字节密钥示例
feedback.url-encryption-key=YWJjZGVmZ2hpamtsbW5vcHFyc3R1dnd4eXoxMjM0NTY=

# 生产环境建议使用环境变量
# feedback.url-encryption-key=${URL_ENCRYPTION_KEY:dGhpc0lzQVNhbXBsZUFlczI1NktleUZvclVybEVuY3J5cHRpb25QdXJwb3Nl}

# 安全建议：
# 1. 定期更换加密密钥
# 2. 使用环境变量或配置管理系统存储密钥
# 3. 确保密钥不被提交到版本控制系统
# 4. 在不同环境（开发、测试、生产）使用不同的密钥
