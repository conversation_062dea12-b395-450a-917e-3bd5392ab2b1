package com.github.cret.web.oee.service.impl;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import com.github.cret.web.common.domain.PageList;
import com.github.cret.web.common.domain.PageableParam;
import com.github.cret.web.common.enumerate.SysErrEnum;
import com.github.cret.web.oee.document.feedback.FeedbackTriggerRecord;
import com.github.cret.web.oee.document.feedback.FeedbackTriggerSolution;
import com.github.cret.web.oee.document.feedback.FeedbackTriggerSolutionSend;
import com.github.cret.web.oee.document.feedback.SolutionHistory;
import com.github.cret.web.oee.domain.query.FeedbackTriggerSolutionQuery;
import com.github.cret.web.oee.domain.request.SolutionRejectRequest;
import com.github.cret.web.oee.repository.FeedbackTriggerSolutionRepository;
import com.github.cret.web.oee.service.FeedbackTriggerSolutionService;
import com.github.cret.web.oee.service.FeedbackTriggerRecordService;
import com.github.cret.web.oee.service.FeedbackTriggerSendService;
import com.github.cret.web.oee.service.FeedbackTriggerSolutionSendService;
import com.github.cret.web.security.domain.AuthUser;
import com.github.cret.web.security.util.SecurityUtil;

@Service
public class FeedbackTriggerSolutionServiceImpl implements FeedbackTriggerSolutionService {

	private static final Logger logger = LoggerFactory.getLogger(FeedbackTriggerSolutionServiceImpl.class);

	private final FeedbackTriggerSolutionRepository repository;

	private final MongoTemplate mongoTemplate;

	private final FeedbackTriggerRecordService feedbackTriggerRecordService;

	private final FeedbackTriggerSendService feedbackTriggerSendService;

	private final FeedbackTriggerSolutionSendService feedbackTriggerSolutionSendService;

	public FeedbackTriggerSolutionServiceImpl(FeedbackTriggerSolutionRepository repository, MongoTemplate mongoTemplate,
			FeedbackTriggerRecordService feedbackTriggerRecordService,
			FeedbackTriggerSendService feedbackTriggerSendService,
			FeedbackTriggerSolutionSendService feedbackTriggerSolutionSendService) {
		this.repository = repository;
		this.mongoTemplate = mongoTemplate;
		this.feedbackTriggerRecordService = feedbackTriggerRecordService;
		this.feedbackTriggerSendService = feedbackTriggerSendService;
		this.feedbackTriggerSolutionSendService = feedbackTriggerSolutionSendService;
	}

	@Override
	@Transactional
	public FeedbackTriggerSolution save(FeedbackTriggerSolution solution) {
		// 校验必填字段
		if (solution.getTriggerRecordId() == null || solution.getTriggerRecordId().trim().isEmpty()) {
			throw SysErrEnum.INVALID_PARAMETER.exception("触发记录ID不能为空");
		}
		if (solution.getSolution() == null || solution.getSolution().trim().isEmpty()) {
			throw SysErrEnum.INVALID_PARAMETER.exception("解决方案内容不能为空");
		}

		// 设置当前用户信息
		AuthUser authUser = SecurityUtil.getCurrentUser();
		Date now = new Date();

		if (solution.getId() == null) {
			// 新建记录
			solution.setCreateTime(now);
			solution.setCreateBy(authUser != null ? authUser.id() : null);

			// 设置解决人信息，如果未设置则使用当前用户
			if (solution.getSolverId() == null && authUser != null) {
				solution.setSolverId(authUser.id());
			}
			if (solution.getSolverName() == null && authUser != null) {
				solution.setSolverName(authUser.name());
			}

			// 设置解决时间，如果未设置则使用当前时间
			if (solution.getSolveTime() == null) {
				solution.setSolveTime(now);
			}

			// 初始设置为未提交
			if (solution.getSubmitted() == null) {
				solution.setSubmitted(false);
			}
		}

		// 设置更新信息
		solution.setUpdateTime(now);
		solution.setUpdateBy(authUser != null ? authUser.id() : null);

		return repository.save(solution);
	}

	@Override
	public void delete(String id) {
		if (!repository.existsById(id)) {
			throw SysErrEnum.NOT_FOUND.exception("解决方案不存在");
		}
		repository.deleteById(id);
	}

	@Override
	public FeedbackTriggerSolution findById(String id) {
		return repository.findById(id).orElseThrow(() -> SysErrEnum.NOT_FOUND.exception("解决方案不存在"));
	}

	@Override
	public PageList<FeedbackTriggerSolution> search(PageableParam<FeedbackTriggerSolutionQuery> query) {
		FeedbackTriggerSolutionQuery searchParams = query.getSearchParams();
		Query mongoQuery = new Query();
		List<Criteria> criteria = new ArrayList<>();

		if (StringUtils.hasText(searchParams.getTriggerRecordId())) {
			criteria.add(Criteria.where("triggerRecordId").is(searchParams.getTriggerRecordId()));
		}

		if (StringUtils.hasText(searchParams.getSolverId())) {
			criteria.add(Criteria.where("solverId").is(searchParams.getSolverId()));
		}

		if (StringUtils.hasText(searchParams.getSolverName())) {
			criteria.add(Criteria.where("solverName").regex(".*" + searchParams.getSolverName() + ".*", "i"));
		}

		if (StringUtils.hasText(searchParams.getSolution())) {
			criteria.add(Criteria.where("solution").regex(".*" + searchParams.getSolution() + ".*", "i"));
		}

		if (searchParams.getSubmited() != null) {
			criteria.add(Criteria.where("submited").is(searchParams.getSubmited()));
		}

		if (searchParams.getStartSolveTime() != null) {
			criteria.add(Criteria.where("solveTime").gte(searchParams.getStartSolveTime()));
		}

		if (searchParams.getEndSolveTime() != null) {
			criteria.add(Criteria.where("solveTime").lte(searchParams.getEndSolveTime()));
		}

		if (!criteria.isEmpty()) {
			mongoQuery.addCriteria(new Criteria().andOperator(criteria.toArray(new Criteria[0])));
		}

		long total = mongoTemplate.count(mongoQuery, FeedbackTriggerSolution.class);

		mongoQuery.with(query.getPageRequest());
		List<FeedbackTriggerSolution> list = mongoTemplate.find(mongoQuery, FeedbackTriggerSolution.class);

		PageList<FeedbackTriggerSolution> result = new PageList<>();
		result.setList(list);
		result.setTotal(total);
		result.setHasNext((long) query.getPageRequest().getOffset() + query.getPageRequest().getPageSize() < total);

		return result;
	}

	@Override
	@Transactional
	public FeedbackTriggerSolution update(String id, FeedbackTriggerSolution solution) {
		FeedbackTriggerSolution existing = findById(id);

		// 保留原有的ID和创建信息
		solution.setId(existing.getId());
		solution.setCreateTime(existing.getCreateTime());
		solution.setCreateBy(existing.getCreateBy());

		solution.setSolverId(existing.getSolverId());
		solution.setSolverName(existing.getSolverName());

		// 保留原有的提交状态和提交时间
		solution.setSubmitted(existing.getSubmitted());
		solution.setSubmitTime(existing.getSubmitTime());

		// 设置更新信息
		AuthUser authUser = SecurityUtil.getCurrentUser();
		solution.setUpdateTime(new Date());
		solution.setUpdateBy(authUser != null ? authUser.id() : null);

		return repository.save(solution);
	}

	@Override
	public List<FeedbackTriggerSolution> findByTriggerRecordId(String triggerRecordId) {
		return repository.findByTriggerRecordId(triggerRecordId);
	}

	@Override
	@Transactional
	public FeedbackTriggerSolution submit(String id) {
		// 获取解决方案
		FeedbackTriggerSolution solution = findById(id);

		// 校验解决方案内容
		if (solution.getSolution() == null || solution.getSolution().trim().isEmpty()) {
			throw SysErrEnum.INVALID_PARAMETER.exception("解决方案内容不能为空，无法提交");
		}

		// 如果之前被退回过，清除退回状态
		if (solution.getRejected() != null && solution.getRejected()) {
			solution.setRejected(false);
			solution.setRejectTime(null);
			solution.setRejectReason(null);
			solution.setRejectorId(null);
			solution.setRejectorName(null);
		}

		// 设置为已提交
		solution.setSubmitted(true);

		// 设置提交时间
		Date now = new Date();
		solution.setSubmitTime(now);

		// 设置更新信息
		AuthUser authUser = SecurityUtil.getCurrentUser();
		solution.setUpdateTime(now);
		solution.setUpdateBy(authUser != null ? authUser.id() : null);

		// 保存解决方案
		FeedbackTriggerSolution savedSolution = repository.save(solution);

		// 创建解决方案发送记录，通知相关用户
		try {
			// 获取触发记录信息
			FeedbackTriggerRecord triggerRecord = feedbackTriggerRecordService.findById(solution.getTriggerRecordId());
			if (triggerRecord != null && triggerRecord.getNoticeUsers() != null
					&& !triggerRecord.getNoticeUsers().isEmpty()) {
				// 创建解决方案通知记录，为每个用户创建单独的发送记录
				List<FeedbackTriggerSolutionSend> solutionSends = feedbackTriggerSolutionSendService
					.createSolutionNotification(savedSolution, triggerRecord.getNoticeUsers());
				logger.info("已创建解决方案通知记录，解决方案ID: {}, 通知用户数: {}, 创建发送记录数: {}", savedSolution.getId(),
						triggerRecord.getNoticeUsers().size(), solutionSends.size());
			}
			else {
				logger.info("触发记录中没有通知用户列表，跳过解决方案通知，解决方案ID: {}", savedSolution.getId());
			}
		}
		catch (Exception e) {
			// 发送通知失败不影响解决方案提交
			logger.warn("创建解决方案通知记录失败，解决方案ID: {}, 错误: {}", savedSolution.getId(), e.getMessage(), e);
		}

		return savedSolution;
	}

	@Override
	public List<FeedbackTriggerSolution> findBySolverId(String solverId) {
		return repository.findBySolverId(solverId);
	}

	@Override
	public FeedbackTriggerSolution findByTriggerRecordIdAndTriggerSendId(String triggerRecordId, String triggerSendId) {
		// 校验参数
		if (triggerRecordId == null || triggerRecordId.trim().isEmpty()) {
			throw SysErrEnum.INVALID_PARAMETER.exception("触发记录ID不能为空");
		}
		if (triggerSendId == null || triggerSendId.trim().isEmpty()) {
			throw SysErrEnum.INVALID_PARAMETER.exception("发送记录ID不能为空");
		}

		// 验证 triggerRecordId 在 t_feedback_trigger_record 表中是否存在
		try {
			feedbackTriggerRecordService.findById(triggerRecordId);
		}
		catch (Exception e) {
			throw SysErrEnum.NOT_FOUND.exception("触发记录不存在，请检查触发记录ID是否正确");
		}

		// 验证 triggerSendId 在 t_feedback_trigger_send 表中是否存在
		try {
			feedbackTriggerSendService.findById(triggerSendId);
		}
		catch (Exception e) {
			throw SysErrEnum.NOT_FOUND.exception("发送记录不存在，请检查发送记录ID是否正确");
		}

		// 查找解决方案
		FeedbackTriggerSolution solution = repository.findByTriggerRecordIdAndTriggerSendId(triggerRecordId,
				triggerSendId);

		return solution;
	}

	@Override
	@Transactional
	public FeedbackTriggerSolution reject(String id, SolutionRejectRequest request) {
		// 校验请求参数
		if (request == null) {
			throw SysErrEnum.INVALID_PARAMETER.exception("退回请求参数不能为空");
		}
		if (request.getRejectReason() == null || request.getRejectReason().trim().isEmpty()) {
			throw SysErrEnum.INVALID_PARAMETER.exception("退回原因不能为空");
		}
		if (request.getRejectorId() == null || request.getRejectorId().trim().isEmpty()) {
			throw SysErrEnum.INVALID_PARAMETER.exception("退回人ID不能为空");
		}
		if (request.getRejectorName() == null || request.getRejectorName().trim().isEmpty()) {
			throw SysErrEnum.INVALID_PARAMETER.exception("退回人名称不能为空");
		}

		// 获取解决方案
		FeedbackTriggerSolution solution = findById(id);

		// 校验解决方案状态
		if (solution.getSubmitted() == null || !solution.getSubmitted()) {
			throw SysErrEnum.INVALID_PARAMETER.exception("只能退回已提交的解决方案");
		}

		// 创建历史记录
		SolutionHistory history = new SolutionHistory();
		history.setSolution(solution.getSolution());
		history.setSolverId(solution.getSolverId());
		history.setSolverName(solution.getSolverName());
		history.setSolveTime(solution.getSolveTime());
		history.setSubmitTime(solution.getSubmitTime());

		// 设置退回信息（使用传入的退回人信息）
		Date now = new Date();

		history.setRejectTime(now);
		history.setRejectReason(request.getRejectReason());
		history.setRejectorId(request.getRejectorId());
		history.setRejectorName(request.getRejectorName());

		// 添加到历史记录
		if (solution.getSolutionHistory() == null) {
			solution.setSolutionHistory(new ArrayList<>());
		}
		solution.getSolutionHistory().add(history);

		// 设置退回状态（使用传入的退回人信息）
		solution.setRejected(true);
		solution.setRejectTime(now);
		solution.setRejectReason(request.getRejectReason());
		solution.setRejectorId(request.getRejectorId());
		solution.setRejectorName(request.getRejectorName());

		// 设置为未提交状态，允许重新编辑
		solution.setSubmitted(false);

		// 设置更新信息（仍使用当前登录用户作为更新人）
		AuthUser authUser = SecurityUtil.getCurrentUser();
		solution.setUpdateTime(now);
		solution.setUpdateBy(authUser != null ? authUser.id() : null);

		// 保存解决方案
		FeedbackTriggerSolution savedSolution = repository.save(solution);

		// 创建退回通知记录，通知处理人
		try {
			feedbackTriggerSolutionSendService.createRejectNotification(savedSolution);
			logger.info("已创建解决方案退回通知记录，解决方案ID: {}, 通知处理人: {}", savedSolution.getId(), savedSolution.getSolverName());
		}
		catch (Exception e) {
			// 发送通知失败不影响解决方案退回
			logger.warn("创建解决方案退回通知记录失败，解决方案ID: {}, 错误: {}", savedSolution.getId(), e.getMessage(), e);
		}

		return savedSolution;
	}

}
