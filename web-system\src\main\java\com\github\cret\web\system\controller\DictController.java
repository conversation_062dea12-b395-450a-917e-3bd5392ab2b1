package com.github.cret.web.system.controller;

import com.github.cret.web.common.annotation.OpLog;
import com.github.cret.web.common.domain.PageList;
import com.github.cret.web.common.domain.PageableParam;
import com.github.cret.web.system.document.Dict;
import com.github.cret.web.system.domain.DictCreate;
import com.github.cret.web.system.domain.DictItem;
import com.github.cret.web.system.domain.DictSimple;
import com.github.cret.web.system.domain.DictUpdate;
import com.github.cret.web.system.service.DictService;
import jakarta.validation.Valid;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RequestMapping("/dict")
@RestController
public class DictController {

	private final DictService dictService;

	public DictController(DictService dictService) {
		this.dictService = dictService;
	}

	@GetMapping("/getByCode/{code}")
	public Dict getByCode(@PathVariable("code") String code) {
		return dictService.getByCode(code);
	}

	@PostMapping("/page")
	public PageList<DictSimple> page(@RequestBody PageableParam<DictSimple> param) {
		return dictService.page(param);
	}

	@PostMapping("/create")
	@OpLog(name = "创建字典")
	public void create(@RequestBody @Valid DictCreate dictCreate) {
		dictService.create(dictCreate);
	}

	@DeleteMapping("/{id}")
	public void delete(@PathVariable("id") String id) {
		dictService.delete(id);
	}

	@GetMapping("/{id}")
	public Dict get(@PathVariable("id") String id) {
		return dictService.get(id);
	}

	@PostMapping("/update/{id}")
	@OpLog(name = "更新字典")
	public void update(@PathVariable("id") String id, @RequestBody DictUpdate dictUpdate) {
		dictService.update(id, dictUpdate);
	}

	@GetMapping("/listItem/{code}")
	public List<DictItem> listItem(@PathVariable("code") String code) {
		return dictService.listItem(code);
	}

}
