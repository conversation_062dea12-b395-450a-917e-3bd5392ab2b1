package com.github.cret.web.oee.utils;

import java.util.ArrayList;
import java.util.List;
import java.util.function.Consumer;
import java.util.function.Supplier;

/**
 * 对象构建工具类
 */
public class BuilderUtil<T> {

	private final Supplier<T> constructor;

	private final List<Consumer<T>> injects = new ArrayList<>();

	private BuilderUtil(Supplier<T> constructor) {
		this.constructor = constructor;
	}

	public static <T> BuilderUtil<T> builder(Supplier<T> constructor) {
		return new BuilderUtil<>(constructor);
	}

	public <P1> BuilderUtil<T> with(DInjectConsumer<T, P1> consumer, P1 p1) {
		injects.add(instance -> consumer.accept(instance, p1));
		return this;
	}

	public <P1, P2> BuilderUtil<T> with(DInjectConsumer2<T, P1, P2> consumer, P1 p1, P2 p2) {
		injects.add(instance -> consumer.accept(instance, p1, p2));
		return this;
	}

	public BuilderUtil<T> with(Consumer<T> action) {
		injects.add(action);
		return this;
	}

	public T build() {
		T instance = constructor.get();
		injects.forEach(inject -> inject.accept(instance));
		return instance;
	}

	@FunctionalInterface
	public interface DInjectConsumer<T, P1> {

		void accept(T t, P1 p1);

	}

	@FunctionalInterface
	public interface DInjectConsumer2<T, P1, P2> {

		void accept(T t, P1 p1, P2 p2);

	}

}
