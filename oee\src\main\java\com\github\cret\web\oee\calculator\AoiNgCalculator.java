package com.github.cret.web.oee.calculator;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import org.bson.Document;
import org.springframework.data.mongodb.core.MongoTemplate;

import com.github.cret.web.oee.document.Device;
import com.github.cret.web.oee.domain.analyze.AnalyzeQuery;
import com.github.cret.web.oee.utils.OeeUtil;

/**
 * 获取AOI NG数据
 */
public class AoiNgCalculator {

	/**
	 * 根据设备类型获取最后一次记录结果为"NG"的序列号列表
	 * @param device 设备对象
	 * @param query 查询条件
	 * @param mongoTemplate MongoDB操作模板
	 * @return
	 */
	public static List<String> findNgSerials(Device device, AnalyzeQuery query, MongoTemplate mongoTemplate) {
		switch (device.getType()) {
			case aoi_yamaha:
				return getYamahaAoiNgSerials(device, query, mongoTemplate);
			case aoi_viscom:
				return getViscomAoiNgSerials(device, query, mongoTemplate);
			case aoi_jutze:
				return getJutzeAoiNgSerials(device, query, mongoTemplate);
			case aoi_delu:
				return Collections.emptyList();
			default:
				throw new UnsupportedOperationException("设备类型 " + device.getType() + " 未实现良品率查询");
		}
	}

	/**
	 * 获取最后一次记录结果为"ng"的序列号列表
	 * @param device 设备对象
	 * @param query 查询条件
	 * @param mongoTemplate MongoDB操作模板
	 * @return 序列号列表
	 */
	public static List<String> getYamahaAoiNgSerials(Device device, AnalyzeQuery query, MongoTemplate mongoTemplate) {
		// 获取生产日志集合名称
		String collectionName = OeeUtil.getProductionLogCollection(device.getCode());

		// 构建聚合管道
		List<Document> pipeline = new ArrayList<>();

		// 步骤1: 按时间和流水号排序，确保后续分组时数组是有序的
		pipeline.add(new Document("$match",
				new Document("time", new Document("$gte", query.getStartTime()).append("$lte", query.getEndTime()))
					.append("foldertype", new Document("$in", List.of("OK", "NG")))));

		// 步骤2: 按时间和流水号排序，确保后续分组时数组是有序的
		pipeline.add(new Document("$sort", new Document("serial", 1).append("time", 1)));

		// 步骤3: 按流水号分组，将每个流水号的所有记录状态和时间收集到一个数组中
		pipeline.add(new Document("$group",
				new Document("_id", "$serial").append("lastResult", new Document("$last", "$result"))));

		// 步骤4: 筛选出最后一次记录结果为 "1" 的分组
		pipeline.add(new Document("$match", new Document("lastResult", "ng")));

		// 步骤5: 格式化输出，只返回serial
		pipeline.add(new Document("$project", new Document("_id", 0).append("serial", "$_id")));

		// 执行聚合查询
		List<Document> results = mongoTemplate.getCollection(collectionName)
			.aggregate(pipeline)
			.into(new ArrayList<>());

		List<String> serials = new ArrayList<>();
		for (Document doc : results) {
			serials.add(doc.getString("serial"));
		}
		return serials;
	}

	/**
	 * 获取最后一次记录结果为"1"的序列号列表
	 * @param device
	 * @param query
	 * @param mongoTemplate
	 * @return
	 */
	public static List<String> getViscomAoiNgSerials(Device device, AnalyzeQuery query, MongoTemplate mongoTemplate) {
		// 获取生产日志集合名称
		String collectionName = OeeUtil.getProductionLogCollection(device.getCode());

		// 构建聚合管道
		List<Document> pipeline = new ArrayList<>();

		// 步骤1: 按时间和流水号排序，确保后续分组时数组是有序的
		pipeline.add(new Document("$match",
				new Document("time", new Document("$gte", query.getStartTime()).append("$lte", query.getEndTime()))
					.append("foldertype", "")));

		// 步骤2: 按时间和流水号排序，确保后续分组时数组是有序的
		pipeline.add(new Document("$sort", new Document("serial", 1).append("time", 1)));

		// 步骤3: 按流水号分组，将每个流水号的所有记录状态和时间收集到一个数组中
		pipeline.add(new Document("$group",
				new Document("_id", "$serial").append("lastResult", new Document("$last", "$result"))));

		// 步骤4: 筛选出最后一次记录结果为 "1" 的分组
		pipeline.add(new Document("$match", new Document("lastResult", "1")));

		// 步骤5: 格式化输出，只返回serial
		pipeline.add(new Document("$project", new Document("_id", 0).append("serial", "$_id")));

		// 执行聚合查询
		List<Document> results = mongoTemplate.getCollection(collectionName)
			.aggregate(pipeline)
			.into(new ArrayList<>());

		List<String> serials = new ArrayList<>();
		for (Document doc : results) {
			serials.add(doc.getString("serial"));
		}
		return serials;
	}

	/**
	 * 获取最后一次记录结果为"FAIL"的序列号列表
	 * @param device
	 * @param query
	 * @param mongoTemplate
	 * @return
	 */
	public static List<String> getJutzeAoiNgSerials(Device device, AnalyzeQuery query, MongoTemplate mongoTemplate) {
		// 获取生产日志集合名称
		String collectionName = OeeUtil.getProductionLogCollection(device.getCode());

		// 构建聚合管道
		List<Document> pipeline = new ArrayList<>();

		// 步骤1: 按时间和流水号排序，确保后续分组时数组是有序的
		pipeline.add(new Document("$match",
				new Document("time", new Document("$gte", query.getStartTime()).append("$lte", query.getEndTime()))
					.append("foldertype", "")));

		// 步骤2: 按时间和流水号排序，确保后续分组时数组是有序的
		pipeline.add(new Document("$sort", new Document("serial", 1).append("time", 1)));

		// 步骤3: 按流水号分组，将每个流水号的所有记录状态和时间收集到一个数组中
		pipeline.add(new Document("$group",
				new Document("_id", "$serial").append("lastResult", new Document("$last", "$result"))));

		// 步骤4: 筛选出最后一次记录结果为 "1" 的分组
		pipeline.add(new Document("$match", new Document("lastResult", "FAIL")));

		// 步骤5: 格式化输出，只返回serial
		pipeline.add(new Document("$project", new Document("_id", 0).append("serial", "$_id")));

		// 执行聚合查询
		List<Document> results = mongoTemplate.getCollection(collectionName)
			.aggregate(pipeline)
			.into(new ArrayList<>());

		List<String> serials = new ArrayList<>();
		for (Document doc : results) {
			serials.add(doc.getString("serial"));
		}
		return serials;
	}

}
