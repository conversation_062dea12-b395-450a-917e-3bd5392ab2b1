package com.github.cret.web.security.controller;

import com.github.cret.web.security.domain.AuthUser;
import com.github.cret.web.security.domain.LoginRes;
import com.github.cret.web.security.service.AuthService;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/auth")
public class AuthController {

	private final AuthService authService;

	public AuthController(AuthService authService) {
		this.authService = authService;
	}

	@PostMapping("/login")
	public LoginRes loginByPassword(@RequestParam String username, @RequestParam String password) {
		return authService.loginByPassword(username, password);
	}

	@PostMapping("/login/casdoor")
	public LoginRes loginByCasdoor(@RequestParam String code, @RequestParam String state) {
		return authService.loginByCasdoor(code, state);
	}

	@GetMapping("/userInfo")
	public AuthUser getUserInfo() {
		return authService.getUserInfo();
	}

	@PostMapping("/refresh")
	public String refreshToken(@RequestParam String refreshToken) {
		return authService.refreshToken(refreshToken);
	}

}
