package com.github.cret.web.oee.calculator;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

import org.bson.Document;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.aggregation.AddFieldsOperation;
import org.springframework.data.mongodb.core.aggregation.Aggregation;
import org.springframework.data.mongodb.core.aggregation.AggregationExpression;
import org.springframework.data.mongodb.core.aggregation.ArithmeticOperators;
import org.springframework.data.mongodb.core.aggregation.ArrayOperators;
import org.springframework.data.mongodb.core.aggregation.GroupOperation;
import org.springframework.data.mongodb.core.aggregation.ProjectionOperation;
import org.springframework.data.mongodb.core.query.Criteria;

import com.github.cret.web.oee.document.Device;
import com.github.cret.web.oee.domain.analyze.AnalyzeQuery;
import com.github.cret.web.oee.domain.analyze.AoiProductionGroup;
import com.github.cret.web.oee.utils.OeeUtil;

/**
 * AOI良品率计算器
 */
public class AoiQualityWithSerialCalculator {

	/**
	 * 根据设备类型获取AOI的总产出数据
	 * @param device 设备对象
	 * @param query 查询条件
	 * @param mongoTemplate MongoDB操作模板
	 * @return 返回实际生产分组数据列表
	 * @throws IllegalArgumentException 如果设备或设备类型为空
	 * @throws UnsupportedOperationException 如果设备类型不支持
	 */
	public static List<AoiProductionGroup> getAoiTotalCount(Device device, AnalyzeQuery query,
			MongoTemplate mongoTemplate) {
		if (device == null || device.getType() == null) {
			throw new IllegalArgumentException("设备类型不能为空");
		}

		switch (device.getType()) {
			case aoi_yamaha:
				return getYamahaAoiTotalCount(device, query, mongoTemplate);
			case aoi_viscom:
				return getViscomAoiTotalCount(device, query, mongoTemplate);
			case aoi_jutze:
				return getJutzeAoiTotalCount(device, query, mongoTemplate);
			case aoi_delu:
				return getDeluAoiTotalCount(device, query, mongoTemplate);
			default:
				throw new UnsupportedOperationException("设备类型 " + device.getType() + " 未实现良品率查询");
		}
	}

	/**
	 * 获取雅马哈AOI的总产出数据
	 * @param device 设备对象
	 * @param query 查询条件
	 * @param mongoTemplate MongoDB操作模板
	 * @return 返回实际生产分组数据列表
	 */
	public static List<AoiProductionGroup> getYamahaAoiTotalCount(Device device, AnalyzeQuery query,
			MongoTemplate mongoTemplate) {
		// 获取生产日志集合名称
		String collectionName = OeeUtil.getProductionLogCollection(device.getCode());

		// 构建基础匹配条件：时间范围
		Criteria matchCriteria = Criteria.where("time")
			.gte(query.getStartTime())
			.lte(query.getEndTime())
			.and("foldertype")
			.in("OK", "NG");

		// 阶段二：核心分组逻辑
		GroupOperation groupOperation = Aggregation.group("productmodel").addToSet("serial").as("uniqueSerials");

		// 阶段三：格式化输出
		ProjectionOperation projectOperation = Aggregation.project()
			.andExclude("_id")
			.and("_id")
			.as("productModel")
			.and(ArrayOperators.Size.lengthOfArray("uniqueSerials"))
			.as("actualProduction");

		// 使用聚合管道来统计数量
		Aggregation aggregation = Aggregation.newAggregation(
				// 阶段一：筛选文档
				Aggregation.match(matchCriteria), groupOperation, projectOperation);

		// 执行聚合查询并返回结果
		List<AoiProductionGroup> results = mongoTemplate
			.aggregate(aggregation, collectionName, AoiProductionGroup.class)
			.getMappedResults();
		return results.stream().peek(e -> e.setNeedFlat(true)).collect(Collectors.toList());
	}

	/**
	 * 获取维视AOI的总产出数据
	 * @param device 设备对象
	 * @param query 查询条件
	 * @param mongoTemplate MongoDB操作模板
	 * @return 返回实际生产分组数据列表
	 */
	public static List<AoiProductionGroup> getViscomAoiTotalCount(Device device, AnalyzeQuery query,
			MongoTemplate mongoTemplate) {
		// 获取生产日志集合名称
		String collectionName = OeeUtil.getProductionLogCollection(device.getCode());

		// 构建基础匹配条件：时间范围和结果条件
		Criteria matchCriteria = Criteria.where("time")
			.gte(query.getStartTime())
			.lte(query.getEndTime())
			// .and("result")
			// .in("0", "1", "2")
			.and("foldertype")
			.is("");

		// 阶段二：核心分组逻辑
		GroupOperation groupOperation = Aggregation.group("productmodel").addToSet("serial").as("uniqueSerials");

		// 阶段三：格式化输出
		ProjectionOperation projectOperation = Aggregation.project()
			.andExclude("_id")
			.and("_id")
			.as("productModel")
			.and(ArrayOperators.Size.lengthOfArray("uniqueSerials"))
			.as("actualProduction");

		// 使用聚合管道来统计数量
		Aggregation aggregation = Aggregation.newAggregation(
				// 阶段一：筛选文档
				Aggregation.match(matchCriteria), groupOperation, projectOperation);

		// 执行聚合查询并返回结果
		List<AoiProductionGroup> results = mongoTemplate
			.aggregate(aggregation, collectionName, AoiProductionGroup.class)
			.getMappedResults();
		return results.stream().peek(e -> e.setNeedFlat(true)).collect(Collectors.toList());
	}

	/**
	 * 计算矩子AOI的总产出数据
	 * @param device 设备对象
	 * @param query 查询条件
	 * @param mongoTemplate MongoDB操作模板
	 * @return 返回实际生产分组数据列表
	 */
	public static List<AoiProductionGroup> getJutzeAoiTotalCount(Device device, AnalyzeQuery query,
			MongoTemplate mongoTemplate) {
		// 获取生产日志集合名称
		String collectionName = OeeUtil.getProductionLogCollection(device.getCode());

		// 构建基础匹配条件：时间范围和结果条件
		Criteria matchCriteria = Criteria.where("time")
			.gte(query.getStartTime())
			.lte(query.getEndTime())
			.and("result")
			.in("PASS", "FAIL")
			.and("foldertype")
			.is("");

		// 阶段1: 提取每个文档的serialnumber数组
		AggregationExpression mapExpression = context -> new Document("$map",
				new Document("input", "$testrecord").append("as", "tr").append("in", "$$tr.serialnumber"));
		ProjectionOperation projectStage1 = Aggregation.project("productmodel").and(mapExpression).as("uniqueSerials");

		// 阶段2: 按productmodel分组，统计文档数并收集serial数组
		GroupOperation groupStage = Aggregation.group("productmodel")
			.count()
			.as("actualProduction")
			.addToSet("uniqueSerials")
			.as("uniqueSerialSet");

		// 阶段3: 合并并计算唯一serial数量，格式化输出
		AggregationExpression reduceExpression = context -> new Document("$reduce",
				new Document("input", "$uniqueSerialSet").append("initialValue", Collections.emptyList())
					.append("in", new Document("$setUnion", Arrays.asList("$$value", "$$this"))));
		AggregationExpression sizeExpression = ArrayOperators.Size.lengthOfArray(reduceExpression);
		ProjectionOperation projectStage3 = Aggregation.project()
			.and("_id")
			.as("productModel")
			.and("actualProduction")
			.as("actualProduction")
			.and(sizeExpression)
			.as("panelCount")
			.andExclude("_id");

		// 阶段4: 计算平均拼板数（向上取整）
		AggregationExpression divideExpr = ArithmeticOperators.valueOf("panelCount").divideBy("actualProduction");
		AggregationExpression ceilExpr = ArithmeticOperators.Ceil.ceilValueOf(divideExpr);
		AddFieldsOperation addFieldsStage = Aggregation.addFields().addField("flatNum").withValue(ceilExpr).build();

		// 构建聚合管道
		Aggregation aggregation = Aggregation.newAggregation(Aggregation.match(matchCriteria), projectStage1,
				groupStage, projectStage3, addFieldsStage);

		// 执行聚合查询并返回结果
		List<AoiProductionGroup> results = mongoTemplate
			.aggregate(aggregation, collectionName, AoiProductionGroup.class)
			.getMappedResults();
		return results.stream().peek(e -> e.setNeedFlat(true)).collect(Collectors.toList());
	}

	/**
	 * 计算德律AOI的总产出数据
	 * @param device 设备对象
	 * @param query 查询条件
	 * @param mongoTemplate MongoDB操作模板
	 * @return 返回实际生产分组数据列表
	 */
	public static List<AoiProductionGroup> getDeluAoiTotalCount(Device device, AnalyzeQuery query,
			MongoTemplate mongoTemplate) {
		// 获取生产日志集合名称
		String collectionName = OeeUtil.getProductionLogCollection(device.getCode());

		// 构建基础匹配条件：时间范围和结果条件
		Criteria matchCriteria = Criteria.where("time")
			.gte(query.getStartTime())
			.lte(query.getEndTime())
			.and("result")
			.in("PASS", "FAIL", "ERROR")
			.and("foldertype")
			.is("");

		// 使用聚合管道来统计数量
		Aggregation aggregation = Aggregation.newAggregation(
				// 匹配时间范围和结果条件
				Aggregation.match(matchCriteria),
				// 按产品型号分组并计数
				Aggregation.group("productmodel").count().as("actualProduction"),
				// 格式化输出
				Aggregation.project()
					.and("_id")
					.as("productModel")
					.and("actualProduction")
					.as("actualProduction")
					.andExclude("_id"));

		// 执行聚合查询并返回结果
		List<AoiProductionGroup> results = mongoTemplate
			.aggregate(aggregation, collectionName, AoiProductionGroup.class)
			.getMappedResults();
		return results.stream().peek(e -> e.setNeedFlat(false)).collect(Collectors.toList());
	}

}
