package com.github.cret.web.oee.domain.analyze;

/**
 * 不良类型信息
 */
public class DefectTypeInfo {

	// 不良类型ID
	private String type;

	// 不良类型名称
	private String typeName;

	// 数量
	private String count;

	public DefectTypeInfo() {
	}

	public DefectTypeInfo(String type, String typeName, String count) {
		this.type = type;
		this.typeName = typeName;
		this.count = count;
	}

	public String getType() {
		return type;
	}

	public void setType(String type) {
		this.type = type;
	}

	public String getTypeName() {
		return typeName;
	}

	public void setTypeName(String typeName) {
		this.typeName = typeName;
	}

	public String getCount() {
		return count;
	}

	public void setCount(String count) {
		this.count = count;
	}

	@Override
	public String toString() {
		return "DefectTypeInfo{" + "type='" + type + '\'' + ", typeName='" + typeName + '\'' + ", count='" + count
				+ '\'' + '}';
	}

}
