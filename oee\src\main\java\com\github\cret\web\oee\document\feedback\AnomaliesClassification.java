package com.github.cret.web.oee.document.feedback;

import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

import java.util.List;

/**
 * 异常分类配置
 */
@Document("t_feedback_anomalies_classification")
public class AnomaliesClassification {

	@Id
	private String id;

	/**
	 * 线体编码
	 */
	@Field(name = "line_code")
	private String lineCode;

	@Field(name = "anomalies_code")
	private String anomaliesCode;

	@Field(name = "anomalies_name")
	private String anomaliesName;

	@Field(name = "enable")
	private Boolean enable;

	@Field(name = "response_config")
	private List<ResponseConfig> responseConfig;

	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	}

	public String getLineCode() {
		return lineCode;
	}

	public void setLineCode(String lineCode) {
		this.lineCode = lineCode;
	}

	public String getAnomaliesCode() {
		return anomaliesCode;
	}

	public void setAnomaliesCode(String anomaliesCode) {
		this.anomaliesCode = anomaliesCode;
	}

	public String getAnomaliesName() {
		return anomaliesName;
	}

	public void setAnomaliesName(String anomaliesName) {
		this.anomaliesName = anomaliesName;
	}

	public Boolean getEnable() {
		return enable;
	}

	public void setEnable(Boolean enable) {
		this.enable = enable;
	}

	public List<ResponseConfig> getResponseConfig() {
		return responseConfig;
	}

	public void setResponseConfig(List<ResponseConfig> responseConfig) {
		this.responseConfig = responseConfig;
	}

}
