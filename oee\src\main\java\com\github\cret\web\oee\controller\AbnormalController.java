package com.github.cret.web.oee.controller;

import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.List;

import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.alibaba.excel.EasyExcel;
import com.github.cret.web.common.annotation.OpLog;
import com.github.cret.web.common.domain.PageList;
import com.github.cret.web.common.domain.PageableParam;
import com.github.cret.web.oee.document.analyze.Abnormal;
import com.github.cret.web.oee.domain.abnormal.AbnormalConfirm;
import com.github.cret.web.oee.domain.abnormal.AbnormalSearch;
import com.github.cret.web.oee.service.AbnormalService;
import com.github.cret.web.security.domain.AuthUser;
import com.github.cret.web.security.util.SecurityUtil;
import com.github.cret.web.system.domain.DictItem;
import com.github.cret.web.system.service.DictService;

import jakarta.servlet.http.HttpServletResponse;

@RestController
@RequestMapping("/abnormal")
public class AbnormalController {

	private final AbnormalService abnormalService;

	private final DictService dictService;

	public AbnormalController(AbnormalService abnormalService, DictService dictService) {
		this.abnormalService = abnormalService;
		this.dictService = dictService;
	}

	@PostMapping
	public Abnormal create(@RequestBody Abnormal abnormal) {
		return abnormalService.save(abnormal);
	}

	@PostMapping("/page")
	public PageList<Abnormal> page(@RequestBody PageableParam<AbnormalSearch> param) {
		return abnormalService.page(param);
	}

	@GetMapping("/{id}")
	public Abnormal findById(@PathVariable String id) {
		return abnormalService.findById(id);
	}

	@PutMapping("/{id}")
	public Abnormal update(@PathVariable String id, @RequestBody Abnormal abnormal) {
		abnormal.setId(id);
		return abnormalService.save(abnormal);
	}

	@PutMapping("/{id}/confirm")
	public Abnormal confirm(@PathVariable String id, @RequestBody AbnormalConfirm confirm) {
		return abnormalService.confirm(id, confirm);
	}

	@PutMapping("/{id}/verified")
	public Abnormal verified(@PathVariable String id) {
		AuthUser authUser = SecurityUtil.getCurrentUser();
		AbnormalConfirm confirm = new AbnormalConfirm();
		confirm.setConfirm(authUser.displayName());
		return abnormalService.confirm(id, confirm);
	}

	@DeleteMapping("/{id}")
	@OpLog(name = "删除异常")
	public void delete(@PathVariable String id) {
		abnormalService.deleteById(id);
	}

	@PostMapping("/export")
	public void exportExcel(HttpServletResponse response, @RequestBody AbnormalSearch param) throws IOException {
		response.setContentType("application/vnd.ms-excel");
		response.setCharacterEncoding("utf-8");
		String fileName = URLEncoder.encode("异常记录", StandardCharsets.UTF_8);
		response.setHeader("Content-disposition", "attachment;filename=" + fileName + ".xlsx");

		// 获取异常数据
		List<Abnormal> dataList = abnormalService.findList(param);

		// 获取字典表
		List<DictItem> listItem = dictService.listItem("ABNORMAL_CODE");

		// 替换classification为label并计算异常时间
		for (Abnormal abnormal : dataList) {
			for (DictItem item : listItem) {
				if (item.getValue().equals(abnormal.getClassification())) {
					abnormal.setClassification(item.getLabel());
					break;
				}
			}
			// 将duration(毫秒)转换为小时
			if (abnormal.getDuration() != null) {
				double hours = abnormal.getDuration() / (1000.0 * 60 * 60); // 毫秒转小时
				abnormal.setAbnormalTime(hours);
			}
		}

		EasyExcel.write(response.getOutputStream(), Abnormal.class).sheet("异常记录").doWrite(dataList);
	}

}
