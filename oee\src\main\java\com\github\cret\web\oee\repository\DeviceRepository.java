package com.github.cret.web.oee.repository;

import java.util.List;
import java.util.Optional;

import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.stereotype.Repository;

import com.github.cret.web.oee.document.Device;
import com.github.cret.web.oee.enums.DeviceCategory;

@Repository
public interface DeviceRepository extends MongoRepository<Device, String> {

	List<Device> findByLineIdOrderBySort(String lineId);

	Optional<Device> findFirstByCode(String code);

	List<Device> findByLineIdAndCategory(String lineId, DeviceCategory category);

	List<Device> findByLineId(String lineId);

}
