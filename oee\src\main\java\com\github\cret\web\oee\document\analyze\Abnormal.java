package com.github.cret.web.oee.document.analyze;

import java.util.Date;

import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;

@Document(collection = "t_abnormal")
@ExcelIgnoreUnannotated
public class Abnormal {

	@Id
	private String id;

	// 产线编码
	@ExcelProperty(value = "产线编码")
	private String lineCode;

	// 异常分类
	@ExcelProperty(value = "异常分类")
	private String classification;

	// 异常原因
	@ExcelProperty(value = "异常原因")
	private String cause;

	// 开始时间
	@ExcelProperty(value = "开始时间")
	private Date startTime;

	// 结束时间
	@ExcelProperty(value = "结束时间")
	private Date endTime;

	// 持续时间
	// @ExcelProperty(value = "异常时间(秒)")
	private Long duration;

	@ExcelProperty(value = "异常时间(h)")
	private Double abnormalTime;

	// 责任人
	@ExcelProperty(value = "责任人")
	private String responsible;

	// 责任部门
	@ExcelProperty(value = "责任部门")
	private String department;

	// 填写人
	@ExcelProperty(value = "填写人")
	private String writer;

	// 填写时间
	@ExcelProperty(value = "填写时间")
	private Date writeTime;

	// 确认人
	@ExcelProperty(value = "确认人")
	private String confirm;

	// 确认时间
	@ExcelProperty(value = "确认时间")
	private Date confirmTime;

	// 解决方案
	@ExcelProperty(value = "解决方案")
	private String solution;

	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	}

	public String getLineCode() {
		return lineCode;
	}

	public void setLineCode(String lineCode) {
		this.lineCode = lineCode;
	}

	public String getClassification() {
		return classification;
	}

	public void setClassification(String classification) {
		this.classification = classification;
	}

	public String getCause() {
		return cause;
	}

	public void setCause(String cause) {
		this.cause = cause;
	}

	public Date getStartTime() {
		return startTime;
	}

	public void setStartTime(Date startTime) {
		this.startTime = startTime;
	}

	public Date getEndTime() {
		return endTime;
	}

	public void setEndTime(Date endTime) {
		this.endTime = endTime;
	}

	public Long getDuration() {
		return duration;
	}

	public void setDuration(Long duration) {
		this.duration = duration;
	}

	public String getResponsible() {
		return responsible;
	}

	public void setResponsible(String responsible) {
		this.responsible = responsible;
	}

	public String getDepartment() {
		return department;
	}

	public void setDepartment(String department) {
		this.department = department;
	}

	public String getWriter() {
		return writer;
	}

	public void setWriter(String writer) {
		this.writer = writer;
	}

	public Date getWriteTime() {
		return writeTime;
	}

	public void setWriteTime(Date writeTime) {
		this.writeTime = writeTime;
	}

	public String getConfirm() {
		return confirm;
	}

	public void setConfirm(String confirm) {
		this.confirm = confirm;
	}

	public Date getConfirmTime() {
		return confirmTime;
	}

	public void setConfirmTime(Date confirmTime) {
		this.confirmTime = confirmTime;
	}

	public String getSolution() {
		return solution;
	}

	public void setSolution(String solution) {
		this.solution = solution;
	}

	public Double getAbnormalTime() {
		return abnormalTime;
	}

	public void setAbnormalTime(Double abnormalTime) {
		this.abnormalTime = abnormalTime;
	}

}
