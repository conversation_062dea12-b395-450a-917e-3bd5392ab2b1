package com.github.cret.web.oee.calculator;

import java.util.ArrayList;
import java.util.List;

import org.bson.Document;
import org.springframework.data.mongodb.core.MongoTemplate;

import com.github.cret.web.oee.document.Device;
import com.github.cret.web.oee.domain.analyze.AnalyzeQuery;
import com.github.cret.web.oee.utils.OeeUtil;

import io.jsonwebtoken.lang.Collections;

/**
 * 获取AOI设备在产品型号变更时的前两个序列号(标准件流水号)
 */
public class AoiFisrtTwoSerialCalculator {

	/**
	 * @param device
	 * @param query
	 * @param mongoTemplate
	 * @return
	 */
	public static List<String> findFirstTwoSerialsOnModelChange(Device device, AnalyzeQuery query,
			MongoTemplate mongoTemplate) {
		switch (device.getType()) {
			case aoi_yamaha:
				return findGeneralFirstTwoSerialsOnModelChange(device, query, mongoTemplate);
			case aoi_viscom:
				return findGeneralFirstTwoSerialsOnModelChange(device, query, mongoTemplate);
			case aoi_jutze:
				return findGeneralFirstTwoSerialsOnModelChange(device, query, mongoTemplate);
			case aoi_delu:
				return Collections.emptyList();
			default:
				throw new UnsupportedOperationException("设备类型 " + device.getType() + " 未实现良品率查询");
		}
	}

	/**
	 * 获取产品型号变更时的前两个序列号
	 * @param device 设备对象
	 * @param query 查询条件
	 * @param mongoTemplate MongoDB操作模板
	 * @return 序列号列表
	 */
	public static List<String> findGeneralFirstTwoSerialsOnModelChange(Device device, AnalyzeQuery query,
			MongoTemplate mongoTemplate) {
		// 获取生产日志集合名称
		String collectionName = OeeUtil.getProductionLogCollection(device.getCode());

		// 构建聚合管道
		List<Document> pipeline = new ArrayList<>();

		// Part 1: 找出所有 productmodel 发生变化的"临界点"
		pipeline.add(new Document("$match",
				new Document("time", new Document("$gte", query.getStartTime()).append("$lte", query.getEndTime()))));

		pipeline.add(new Document("$sort", new Document("time", 1)));

		pipeline.add(new Document("$setWindowFields",
				new Document("sortBy", new Document("time", 1)).append("output",
						new Document("previousModel", new Document("$shift",
								new Document("output", "$productmodel").append("by", -1).append("default", null))))));

		pipeline.add(new Document("$match", new Document("previousModel", new Document("$ne", null)).append("$expr",
				new Document("$ne", List.of("$productmodel", "$previousModel")))));

		pipeline.add(new Document("$project",
				new Document("_id", 0).append("changeTime", "$time")
					.append("oldModel", "$previousModel")
					.append("newModel", "$productmodel")));

		// Part 2: 对每个"临界点"，查找新模型的前两条数据
		pipeline
			.add(new Document("$lookup",
					new Document("from", collectionName)
						.append("let", new Document("model", "$newModel").append("startTime", "$changeTime"))
						.append("pipeline", List.of(
								new Document("$match",
										new Document("$expr", new Document("$and",
												List.of(new Document("$eq", List.of("$productmodel", "$$model")),
														new Document("$gte", List.of("$time", "$$startTime")))))),
								new Document("$sort", new Document("time", 1)), new Document("$limit", 2),
								new Document("$project", new Document("_id", 0).append("serial", 1).append("time", 1))))
						.append("as", "firstTwoSerialsOfNewModel")));

		// Part 3: 汇总所有 serial 到一个去重的数组
		pipeline.add(new Document("$unwind", "$firstTwoSerialsOfNewModel"));

		pipeline.add(new Document("$group", new Document("_id", null).append("allSerials",
				new Document("$addToSet", "$firstTwoSerialsOfNewModel.serial"))));

		pipeline.add(new Document("$project", new Document("_id", 0).append("serials", "$allSerials")));

		// 执行聚合查询
		Document result = mongoTemplate.getCollection(collectionName).aggregate(pipeline).first();

		if (result == null) {
			return new ArrayList<>();
		}

		@SuppressWarnings("unchecked")
		List<String> serials = (List<String>) result.get("serials");
		return serials != null ? serials : new ArrayList<>();
	}

}
