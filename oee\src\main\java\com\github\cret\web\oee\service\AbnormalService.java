package com.github.cret.web.oee.service;

import java.util.List;

import com.github.cret.web.common.domain.PageList;
import com.github.cret.web.common.domain.PageableParam;
import com.github.cret.web.oee.document.analyze.Abnormal;
import com.github.cret.web.oee.domain.abnormal.AbnormalConfirm;
import com.github.cret.web.oee.domain.abnormal.AbnormalSearch;

/**
 * 异常处理服务
 */
public interface AbnormalService {

	// 保存异常
	Abnormal save(Abnormal abnormal);

	// 确认异常
	Abnormal confirm(String id, AbnormalConfirm confirm);

	// 分页查询异常
	PageList<Abnormal> page(PageableParam<AbnormalSearch> param);

	// 根据ID查询异常
	Abnormal findById(String id);

	// 删除异常
	void deleteById(String id);

	// 查询理论产出列表
	List<Abnormal> findList(AbnormalSearch param);

}
