package com.github.cret.web.oee.domain.query;

import com.github.cret.web.common.domain.PageableParam;
import java.util.Date;

public class FeedbackTriggerRecordQuery extends PageableParam<FeedbackTriggerRecordQuery> {

	private String anomaliesCode;

	private String lineCode;

	private String triggerId;

	private Date startTime;

	private Date endTime;

	public String getAnomaliesCode() {
		return anomaliesCode;
	}

	public void setAnomaliesCode(String anomaliesCode) {
		this.anomaliesCode = anomaliesCode;
	}

	public String getLineCode() {
		return lineCode;
	}

	public void setLineCode(String lineCode) {
		this.lineCode = lineCode;
	}

	public String getTriggerId() {
		return triggerId;
	}

	public void setTriggerId(String triggerId) {
		this.triggerId = triggerId;
	}

	public Date getStartTime() {
		return startTime;
	}

	public void setStartTime(Date startTime) {
		this.startTime = startTime;
	}

	public Date getEndTime() {
		return endTime;
	}

	public void setEndTime(Date endTime) {
		this.endTime = endTime;
	}

}