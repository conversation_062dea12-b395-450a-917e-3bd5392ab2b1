package com.github.cret.web.security.spring.config;

import com.github.cret.web.security.domain.AuthUser;
import org.springframework.data.domain.AuditorAware;
import org.springframework.lang.NonNull;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;

import java.util.Optional;

public class SecurityAuditorAware implements AuditorAware<String> {

	@Override
	@NonNull
	public Optional<String> getCurrentAuditor() {
		Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
		AuthUser user = (AuthUser) authentication.getPrincipal();
		return Optional.ofNullable(user.name());
	}

}