package com.github.cret.web.oee.domain.print_gkg;

import org.springframework.data.mongodb.core.mapping.Field;

public record PrinterRecord(
		// 标签
		@Field("tag") String tag,
		// 时间戳
		@Field("timestamp") String timeStamp,
		// 机器名称
		@Field("machinename") String machineName,
		// 条形码
		@Field("barcode") String barCode,
		// 文件名
		@Field("filename") String fileName,
		// 程序运行状态
		@Field("programrunning") String programRunning,
		// PCB编号
		@Field("pcbnumber") String pcbNumber,
		// 产品名称
		@Field("prodname") String prodName,
		// 产品类型
		@Field("prodtype") String prodType,
		// 周期时间
		@Field("cycletime") double cycleTime,
		// 2D锡膏检测结果
		@Field("spiresult2d") String spiResult2D,
		// 印刷速度
		@Field("printspeed") double printSpeed,
		// 前刮刀压力
		@Field("frontsqgpress") double frontSQGPress,
		// 后刮刀压力
		@Field("rearsqgpress") double rearSQGPress,
		// 印刷模式
		@Field("printmode") String printMode,
		// 印刷间隙
		@Field("printgap") double printGap,
		// 分离距离
		@Field("snapoffdistance") double snapOffDistance,
		// 分离速度
		@Field("snapoffspeed") double snapOffSpeed,
		// 分离延时
		@Field("snapoffdelay") int snapOffDelay,
		// 刮刀上升速度
		@Field("sqgupspeed") double sqgUpSpeed,
		// 刮刀下降速度
		@Field("sqgdownspeed") double sqgDownSpeed,
		// 刮刀优先上升
		@Field("sqgupfirst") String sqgUpFirst,
		// 分离时刮刀高度
		@Field("sqgheightatsnapoff") double sqgHeightAtSnapOff,
		// 最后一块板后清洗
		@Field("cleaningafterlastboard") boolean cleaningAfterLastBoard,
		// 清洗频率
		@Field("cleaningfrequency") int cleaningFrequency,
		// 清洗速度
		@Field("cleaningspeed") double cleaningSpeed,
		// 清洗模式
		@Field("cleaningmode") String cleaningMode,
		// 清洗类型
		@Field("cleaningtype") String cleaningType,
		// 加锡膏模式
		@Field("addspmode") String addSPMode,
		// 最后一块板后加锡膏
		@Field("addspafterlastboard") int addSPAfterLastBoard,
		// 3D锡膏检测结果
		@Field("spiresult3d") String spiResult3D,
		// 印刷方向
		@Field("printdirection") String printDirection,
		// PCB尺寸
		@Field("pcbsize") String pcbSize,
		// 台面上升X位置
		@Field("tableupx") double tableUpX,
		// 台面上升Y1位置
		@Field("tableupy1") double tableUpY1,
		// 台面上升Y2位置
		@Field("tableupy2") double tableUpY2,
		// X前进
		@Field("xforward") double xForward,
		// Y1前进
		@Field("y1forward") double y1Forward,
		// Y2前进
		@Field("y2forward") double y2Forward,
		// X后退
		@Field("xbackward") double xBackward,
		// Y1后退
		@Field("y1backward") double y1Backward,
		// Y2后退
		@Field("y2backward") double y2Backward,
		// 温度
		@Field("temperature") double temperature,
		// 湿度
		@Field("humidity") double humidity,
		// 标记偏差
		@Field("markdeviation") double markDeviation,
		// 台面到印刷位置
		@Field("tabletoprintingpos") double tableToPrintingPos,
		// 台面到分离位置
		@Field("tabletosnapoffpos") double tableToSnapOffPos) {

}
