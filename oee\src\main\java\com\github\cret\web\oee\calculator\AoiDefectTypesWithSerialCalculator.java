package com.github.cret.web.oee.calculator;

import java.util.Collections;
import java.util.List;

import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.aggregation.Aggregation;
import org.springframework.data.mongodb.core.aggregation.AggregationResults;
import org.springframework.data.mongodb.core.query.Criteria;

import com.github.cret.web.oee.document.Device;
import com.github.cret.web.oee.domain.analyze.AnalyzeQuery;
import com.github.cret.web.oee.domain.analyze.DefectTypeInfo;
import com.github.cret.web.oee.utils.OeeUtil;

/**
 * 依据流水号获取AOI不良类型
 */
public class AoiDefectTypesWithSerialCalculator {

	/**
	 * 计算AOI的不良类型，根据设备类型，选择相应的方法计算不良类型
	 * @param device
	 * @param query 查询条件
	 * @param mongoTemplate 数据库操作对象
	 * @param serialList 流水号列表
	 * @return
	 */
	public static List<DefectTypeInfo> getAoiDefectTypes(Device device, AnalyzeQuery query, MongoTemplate mongoTemplate,
			List<String> serialList) {
		switch (device.getType()) {
			case aoi_yamaha:
				return getYamahaAoiDefectTypes(device, query, mongoTemplate, serialList);
			case aoi_viscom:
				return getViscomAoiDefectTypes(device, query, mongoTemplate, serialList);
			case aoi_jutze:
				return getJutzeAoiDefectTypes(device, query, mongoTemplate, serialList);
			case aoi_delu:
				return getDeluAoiDefectTypes(device, query, mongoTemplate, serialList);
			default:
				throw new UnsupportedOperationException("设备类型 " + device.getType() + " 未实现不良类型查询");
		}
	}

	/**
	 * 计算雅马哈AOI的不良类型
	 * @param device 设备
	 * @param query 查询条件
	 * @param mongoTemplate 数据库操作对象
	 * @param serialList 流水号列表
	 * @return 不良类型
	 */
	public static List<DefectTypeInfo> getYamahaAoiDefectTypes(Device device, AnalyzeQuery query,
			MongoTemplate mongoTemplate, List<String> serialList) {

		// ng列表为空时，直接返回空数组
		if (serialList == null || serialList.isEmpty()) {
			return Collections.emptyList();
		}

		// 获取生产日志集合
		String collectionName = OeeUtil.getProductionLogCollection(device.getCode());

		// 构建基础匹配条件
		Criteria matchCriteria = Criteria.where("time")
			.gte(query.getStartTime())
			.lte(query.getEndTime())
			.and("result")
			.is("ng")
			.and("foldertype")
			.is("NG");

		// 如果指定了产品型号，添加产品型号过滤条件
		if (query.getProductModel() != null && !query.getProductModel().isEmpty()) {
			matchCriteria.and("productmodel").is(query.getProductModel());
		}

		// 如果指定了流水号，添加流水号过滤条件
		if (serialList != null && !serialList.isEmpty()) {
			matchCriteria.and("serial").in(serialList);
		}

		// 使用聚合管道统计不良类型
		Aggregation aggregation = Aggregation.newAggregation(
				// 匹配条件
				Aggregation.match(matchCriteria),
				// 展开testrecord数组
				Aggregation.unwind("raw"),
				// 按文档ID和softwareversion1去重
				Aggregation.group("_id", "raw.softwareversion1").first("raw.defecttype").as("description"),
				// 按description分组统计
				Aggregation.group("description").count().as("count"),
				// 格式化输出
				Aggregation.project().and("_id").as("type").and("count").as("count").andExclude("_id"),
				// 按数量降序排序
				Aggregation.sort(Sort.Direction.DESC, "count"));

		AggregationResults<DefectTypeInfo> results = mongoTemplate.aggregate(aggregation, collectionName,
				DefectTypeInfo.class);

		return results.getMappedResults();
	}

	/**
	 * 计算viscom AOI的不良类型
	 * @param device
	 * @param query
	 * @param mongoTemplate
	 * @param serialList 流水号列表
	 * @return
	 */
	public static List<DefectTypeInfo> getViscomAoiDefectTypes(Device device, AnalyzeQuery query,
			MongoTemplate mongoTemplate, List<String> serialList) {

		// ng列表为空时，直接返回空数组
		if (serialList == null || serialList.isEmpty()) {
			return Collections.emptyList();
		}

		// 获取生产日志集合
		String collectionName = OeeUtil.getProductionLogCollection(device.getCode());

		// 构建基础匹配条件
		Criteria matchCriteria = Criteria.where("time")
			.gte(query.getStartTime())
			.lte(query.getEndTime())
			.and("result")
			.is("1")
			.and("foldertype")
			.is(""); // 只查询不良品记录

		// 如果指定了产品型号，添加产品型号过滤条件
		if (query.getProductModel() != null && !query.getProductModel().isEmpty()) {
			matchCriteria.and("productmodel").is(query.getProductModel());
		}

		// 如果指定了流水号，添加流水号过滤条件
		if (serialList != null && !serialList.isEmpty()) {
			matchCriteria.and("serial").in(serialList);
		}

		// 使用聚合管道统计不良类型
		Aggregation aggregation = Aggregation.newAggregation(
				// 匹配条件
				Aggregation.match(matchCriteria),
				// 展开testrecord数组
				Aggregation.unwind("testrecord"),
				// 按文档ID和boardnumber去重
				Aggregation.group("_id", "testrecord.boardnumber").first("testrecord.description").as("description"),
				// 按description分组统计
				Aggregation.group("description").count().as("count"),
				// 格式化输出
				Aggregation.project().and("_id").as("type").and("count").as("count").andExclude("_id"),
				// 按数量降序排序
				Aggregation.sort(Sort.Direction.DESC, "count"));

		AggregationResults<DefectTypeInfo> results = mongoTemplate.aggregate(aggregation, collectionName,
				DefectTypeInfo.class);

		return results.getMappedResults();
	}

	/**
	 * 计算矩子AOI的不良类型
	 * @param device
	 * @param query
	 * @param mongoTemplate
	 * @param serialList 流水号列表
	 * @return
	 */
	public static List<DefectTypeInfo> getJutzeAoiDefectTypes(Device device, AnalyzeQuery query,
			MongoTemplate mongoTemplate, List<String> serialList) {

		// ng列表为空时，直接返回空数组
		if (serialList == null || serialList.isEmpty()) {
			return Collections.emptyList();
		}

		// 获取动态集合名称（根据设备编号）
		String collectionName = OeeUtil.getProductionLogCollection(device.getCode());

		// 构建基础匹配条件
		Criteria matchCriteria = Criteria.where("time")
			.gte(query.getStartTime())
			.lte(query.getEndTime())
			.and("result")
			.is("FAIL")
			.and("foldertype")
			.is(""); // 只查询不良品记录

		// 可选的产品型号过滤
		if (query.getProductModel() != null && !query.getProductModel().isEmpty()) {
			matchCriteria.and("productmodel").is(query.getProductModel());
		}

		// 如果指定了流水号，添加流水号过滤条件
		if (serialList != null && !serialList.isEmpty()) {
			matchCriteria.and("serial").in(serialList);
		}

		// 构建聚合管道
		Aggregation aggregation = Aggregation.newAggregation(
				// 匹配条件
				Aggregation.match(matchCriteria),
				// 展开测试记录
				Aggregation.unwind("testrecord"),
				// 第一阶段分组：按序列号去重
				Aggregation.group("_id", "testrecord.serialnumber").first("testrecord.errortype").as("errortype"),
				// 第二阶段分组：统计错误类型数量
				Aggregation.group("errortype").count().as("count"),
				// 字段重命名
				Aggregation.project().and("_id").as("type").and("count").as("count").andExclude("_id"),
				// 按数量降序排序
				Aggregation.sort(Sort.Direction.DESC, "count"));

		// 执行查询
		AggregationResults<DefectTypeInfo> results = mongoTemplate.aggregate(aggregation, collectionName,
				DefectTypeInfo.class);

		return results.getMappedResults();
	}

	/**
	 * 计算德律AOI的不良类型
	 * @param device
	 * @param query
	 * @param mongoTemplate
	 * @param serialList
	 * @return
	 */
	public static List<DefectTypeInfo> getDeluAoiDefectTypes(Device device, AnalyzeQuery query,
			MongoTemplate mongoTemplate, List<String> serialList) {
		// 获取动态集合名称（根据设备编号）
		String collectionName = OeeUtil.getProductionLogCollection(device.getCode());

		// 构建基础匹配条件
		Criteria matchCriteria = Criteria.where("time")
			.gte(query.getStartTime())
			.lte(query.getEndTime())
			.and("firstResult")
			.is("FAIL")
			.and("foldertype")
			.is(""); // 只查询不良品记录

		// 可选的产品型号过滤
		if (query.getProductModel() != null && !query.getProductModel().isEmpty()) {
			matchCriteria.and("productmodel").is(query.getProductModel());
		}

		// 如果指定了流水号，添加流水号过滤条件
		if (serialList != null && !serialList.isEmpty()) {
			matchCriteria.and("serial").in(serialList);
		}

		// 构建聚合管道
		Aggregation aggregation = Aggregation.newAggregation(
				// 匹配条件
				Aggregation.match(matchCriteria),
				// 展开测试记录
				Aggregation.unwind("testrecord"),
				// 第一阶段分组：按序列号去重
				Aggregation.group("_id", "testrecord.serialnumber").first("testrecord.errortype").as("errortype"),
				// 第二阶段分组：统计错误类型数量
				Aggregation.group("errortype").count().as("count"),
				// 字段重命名
				Aggregation.project().and("_id").as("type").and("count").as("count").andExclude("_id"),
				// 按数量降序排序
				Aggregation.sort(Sort.Direction.DESC, "count"));

		// 执行查询
		AggregationResults<DefectTypeInfo> results = mongoTemplate.aggregate(aggregation, collectionName,
				DefectTypeInfo.class);

		return results.getMappedResults();
	}

}
