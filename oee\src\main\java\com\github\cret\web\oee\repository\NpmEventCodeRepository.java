package com.github.cret.web.oee.repository;

import java.util.List;

import org.springframework.data.mongodb.repository.MongoRepository;
import com.github.cret.web.oee.document.event.NpmEventCode;
import com.github.cret.web.oee.enums.EventType;

public interface NpmEventCodeRepository extends MongoRepository<NpmEventCode, String> {

	// 可以添加自定义查询方法
	List<NpmEventCode> findByMainCode(String mainCode);

	List<NpmEventCode> findByEventType(EventType eventType);

}