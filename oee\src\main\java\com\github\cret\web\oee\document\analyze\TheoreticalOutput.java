package com.github.cret.web.oee.document.analyze;

import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.index.CompoundIndex;
import org.springframework.data.mongodb.core.index.CompoundIndexes;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;

/**
 * 设备产品理论产出值
 */
@CompoundIndexes({ @CompoundIndex(name = "device_product_index", def = "{'deviceCode': 1, 'productModel': 1}",
		background = true) })
@Document(collection = "theoretical_output")
@ExcelIgnoreUnannotated
public class TheoreticalOutput {

	@Id
	@ExcelProperty(value = "ID")
	private String id;

	/**
	 * 线体编码
	 */
	@ExcelProperty(value = "线体编码")
	@Field(name = "lineCode")
	private String lineCode;

	/**
	 * 设备编码
	 */
	@ExcelProperty(value = "设备编码")
	@Field(name = "deviceCode")
	private String deviceCode;

	/**
	 * 设备类型
	 */
	@ExcelProperty(value = "设备类型")
	@Field(name = "deviceType")
	private String deviceType;

	/**
	 * 产品型号
	 */
	@ExcelProperty(value = "产品型号")
	@Field(name = "productModel")
	private String productModel;

	/**
	 * 每小时理论产出
	 */
	@Field(name = "hourlyOutput")
	private Integer hourlyOutput;

	/**
	 * 理论ct
	 */
	@ExcelProperty(value = "理论ct")
	@Field(name = "ct")
	private Double ct;

	/**
	 * 线体拼板数
	 */
	@ExcelProperty(value = "拼板数")
	@Field(name = "flatNumber")
	private Integer flatNumber;

	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	}

	public String getLineCode() {
		return lineCode;
	}

	public void setLineCode(String lineCode) {
		this.lineCode = lineCode;
	}

	public String getDeviceCode() {
		return deviceCode;
	}

	public void setDeviceCode(String deviceCode) {
		this.deviceCode = deviceCode;
	}

	public String getDeviceType() {
		return deviceType;
	}

	public void setDeviceType(String deviceType) {
		this.deviceType = deviceType;
	}

	public String getProductModel() {
		return productModel;
	}

	public void setProductModel(String productModel) {
		this.productModel = productModel;
	}

	public Integer getHourlyOutput() {
		return hourlyOutput;
	}

	public void setHourlyOutput(Integer hourlyOutput) {
		this.hourlyOutput = hourlyOutput;
	}

	public Double getCt() {
		return ct;
	}

	public void setCt(Double ct) {
		this.ct = ct;
	}

	public Integer getFlatNumber() {
		return flatNumber;
	}

	public void setFlatNumber(Integer flatNumber) {
		this.flatNumber = flatNumber;
	}

}
