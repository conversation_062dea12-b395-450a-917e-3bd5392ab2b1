package com.github.cret.web.oee.repository;

import java.util.List;
import java.util.Optional;

import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.stereotype.Repository;

import com.github.cret.web.oee.document.ProductionLine;

@Repository
public interface ProductionLineRepository extends MongoRepository<ProductionLine, String> {

	Optional<ProductionLine> findByCode(String code);

	List<ProductionLine> findByWorkshopCode(String workshopCode);

}
