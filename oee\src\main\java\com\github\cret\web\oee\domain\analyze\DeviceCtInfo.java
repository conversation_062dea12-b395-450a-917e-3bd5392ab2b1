package com.github.cret.web.oee.domain.analyze;

/**
 * 设备ct信息
 */
public class DeviceCtInfo {

	// 轨道名称
	String trackName;

	// 设备编码
	String code;

	// 设备类型
	String type;

	// 实际ct
	double actualCt;

	// 理论ct
	double theoreticalCt;

	// 是否为瓶颈工序
	boolean isBottleneck;

	// 是否使用
	Integer enable;

	public String getTrackName() {
		return trackName;
	}

	public void setTrackName(String trackName) {
		this.trackName = trackName;
	}

	public String getCode() {
		return code;
	}

	public void setCode(String code) {
		this.code = code;
	}

	public String getType() {
		return type;
	}

	public void setType(String type) {
		this.type = type;
	}

	public double getActualCt() {
		return actualCt;
	}

	public void setActualCt(double actualCt) {
		this.actualCt = actualCt;
	}

	public double getTheoreticalCt() {
		return theoreticalCt;
	}

	public void setTheoreticalCt(double theoreticalCt) {
		this.theoreticalCt = theoreticalCt;
	}

	public boolean isBottleneck() {
		return isBottleneck;
	}

	public void setBottleneck(boolean isBottleneck) {
		this.isBottleneck = isBottleneck;
	}

	public Integer getEnable() {
		return enable;
	}

	public void setEnable(Integer enable) {
		this.enable = enable;
	}

}