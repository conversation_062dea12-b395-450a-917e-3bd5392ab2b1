package com.github.cret.web.oee.domain.analyze;

import java.util.List;

import com.github.cret.web.oee.document.Device;

/**
 * 设备生产信息
 */
public class ProductionDataWithDevice {

	private Device device;

	private List<ProductionData> productionData;

	private List<ProductionData> productionDataGroup;

	public ProductionDataWithDevice(Device device, List<ProductionData> productionData) {
		this.device = device;
		this.productionData = productionData;
	}

	public Device getDevice() {
		return device;
	}

	public void setDevice(Device device) {
		this.device = device;
	}

	public List<ProductionData> getProductionData() {
		return productionData;
	}

	public void setProductionData(List<ProductionData> productionData) {
		this.productionData = productionData;
	}

	public List<ProductionData> getProductionDataGroup() {
		return productionDataGroup;
	}

	public void setProductionDataGroup(List<ProductionData> productionDataGroup) {
		this.productionDataGroup = productionDataGroup;
	}

}