package com.github.cret.web.oee.service.impl;

import java.util.List;
import java.util.stream.Collectors;

import org.springframework.stereotype.Service;

import com.github.cret.web.oee.document.mes.LabelLatestStatus;
import com.github.cret.web.oee.repository.LbwpRepository;
import com.github.cret.web.oee.service.LbwpService;

@Service
public class LbwpServiceImpl implements LbwpService {

	private final LbwpRepository lbwpRepository;

	public LbwpServiceImpl(LbwpRepository lbwpRepository) {
		this.lbwpRepository = lbwpRepository;
	}

	@Override
	public List<LabelLatestStatus> findLatestPassStatusByLbIds(List<String> lbIds) {
		// 提取所有pass的数据
		return lbwpRepository.findAllByLbIdIn(lbIds)
			.stream()
			.filter(data -> "Y".equals(data.getLatestIsPass()))
			.collect(Collectors.toList());
	}

}
