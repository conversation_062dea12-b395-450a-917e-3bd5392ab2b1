package com.github.cret.web.oee.utils;

public class DocumentUtil {

	public static double getDouble(Object value) {
		double result;
		if (value instanceof Integer) {
			result = Double.valueOf((Integer) value);
		}
		else if (value instanceof Double) {
			result = (double) value;
		}
		else if (value instanceof Long) {
			result = Double.valueOf((Long) value);
		}
		else {
			result = 0.0;
		}
		return result;
	}

}
