package com.github.cret.web.oee.domain.analyze;

import java.util.List;

import com.github.cret.web.oee.enums.LineType;

/**
 * 生产线信息
 */
public class ProductionLineResult {

	// 线体信息
	List<ProductionLineInfo> productionLineInfos;

	// 线体类型
	LineType productionLineType;

	public List<ProductionLineInfo> getProductionLineInfos() {
		return productionLineInfos;
	}

	public void setProductionLineInfos(List<ProductionLineInfo> productionLineInfos) {
		this.productionLineInfos = productionLineInfos;
	}

	public LineType getProductionLineType() {
		return productionLineType;
	}

	public void setProductionLineType(LineType productionLineType) {
		this.productionLineType = productionLineType;
	}

}
