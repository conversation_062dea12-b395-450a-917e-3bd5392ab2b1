package com.github.cret.web.oee.domain.wxwork;

// TextCardContent 是文本卡片消息的内容体
public class TextCardContent {

	// 卡片标题，不超过128字节，必填
	private String title;

	// 卡片描述，不超过512字节，支持换行，必填
	private String description;

	// 卡片点击后跳转的链接，必填
	private String url;

	// 按钮文字，默认为“详情”，不超过4个字
	private String btntxt;

	public String getTitle() {
		return title;
	}

	public void setTitle(String title) {
		this.title = title;
	}

	public String getDescription() {
		return description;
	}

	public void setDescription(String description) {
		this.description = description;
	}

	public String getUrl() {
		return url;
	}

	public void setUrl(String url) {
		this.url = url;
	}

	public String getBtntxt() {
		return btntxt;
	}

	public void setBtntxt(String btntxt) {
		this.btntxt = btntxt;
	}

}