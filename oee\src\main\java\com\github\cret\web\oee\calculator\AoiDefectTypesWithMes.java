package com.github.cret.web.oee.calculator;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.aggregation.Aggregation;
import org.springframework.data.mongodb.core.aggregation.AggregationResults;
import org.springframework.data.mongodb.core.query.Criteria;

import com.github.cret.web.oee.document.BadItemDoc;
import com.github.cret.web.oee.document.Device;
import com.github.cret.web.oee.domain.analyze.AnalyzeQuery;
import com.github.cret.web.oee.repository.BadItemRepository;
import com.github.cret.web.oee.domain.analyze.DefectTypeInfo;

/**
 * 依据MES中同步的数据计算AOI不良类型
 */
public class AoiDefectTypesWithMes {

	/**
	 * 查询指定线体指定日期一次直通的不良类型
	 * @param device 设备信息
	 * @param query 查询条件
	 * @param mongoTemplate MongoDB操作模板
	 * @param badItemRepository 不良项目Repository
	 * @return 不良类型统计列表
	 */
	public static List<DefectTypeInfo> getFirstPassDefectTypes(Device device, AnalyzeQuery query,
			MongoTemplate mongoTemplate, BadItemRepository badItemRepository) {
		// 构建查询条件：一次直通失败（keyWpFirstResult = "N"）
		Criteria matchCriteria = Criteria.where("lineCode")
			.is(query.getCode())
			.and("finalWpCmpDate")
			.gte(query.getStartTime())
			.lte(query.getEndTime())
			.and("keyWpFirstResult")
			.is("N")
			.and("qcBadItems")
			.exists(true)
			.ne(null);

		// 使用聚合管道统计不良类型
		Aggregation aggregation = Aggregation.newAggregation(
				// 匹配条件
				Aggregation.match(matchCriteria),
				// 展开QC不良项目数组
				Aggregation.unwind("qcBadItems"),
				// 按标签ID和不良项目ID去重
				Aggregation.group("lbGrp", "qcBadItems.badItemId", "qcBadItems.fieldEx2")
					.first("qcBadItems.badItemId")
					.as("badItemId"),
				// 按不良项目ID分组统计
				Aggregation.group("badItemId").count().as("count"),
				// 格式化输出
				Aggregation.project().and("_id").as("type").and("count").as("count").andExclude("_id"),
				// 按数量降序排序
				Aggregation.sort(Sort.Direction.DESC, "count"));

		AggregationResults<DefectTypeInfo> results = mongoTemplate.aggregate(aggregation,
				"t_mes_label_work_process_with_qc_bad_items", DefectTypeInfo.class);

		// 结合字典表转换不良类型名称
		return enrichWithBadItemNames(results.getMappedResults(), badItemRepository);
	}

	/**
	 * 查询指定线体指定日期复判后的不良类型
	 * @param device 设备信息
	 * @param query 查询条件
	 * @param mongoTemplate MongoDB操作模板
	 * @param badItemRepository 不良项目Repository
	 * @return 不良类型统计列表
	 */
	public static List<DefectTypeInfo> getRejudgedDefectTypes(Device device, AnalyzeQuery query,
			MongoTemplate mongoTemplate, BadItemRepository badItemRepository) {
		// 构建查询条件：复判后失败（keyWpFirstResult = "Y" 但 finalResult = "N"）
		Criteria matchCriteria = Criteria.where("lineCode")
			.is(query.getCode())
			.and("finalWpCmpDate")
			.gte(query.getStartTime())
			.lte(query.getEndTime())
			.and("keyWpFirstResult")
			.is("Y")
			.and("finalResult")
			.is("N")
			.and("qcBadItems")
			.exists(true)
			.ne(null);

		// 使用聚合管道统计不良类型
		Aggregation aggregation = Aggregation.newAggregation(
				// 匹配条件
				Aggregation.match(matchCriteria),
				// 展开QC不良项目数组
				Aggregation.unwind("qcBadItems"),
				// 按标签ID和不良项目ID去重
				Aggregation.group("lbGrp", "qcBadItems.badItemId", "qcBadItems.fieldEx2")
					.first("qcBadItems.badItemId")
					.as("badItemId"),
				// 按不良项目ID分组统计
				Aggregation.group("badItemId").count().as("count"),
				// 格式化输出
				Aggregation.project().and("_id").as("type").and("count").as("count").andExclude("_id"),
				// 按数量降序排序
				Aggregation.sort(Sort.Direction.DESC, "count"));

		AggregationResults<DefectTypeInfo> results = mongoTemplate.aggregate(aggregation,
				"t_mes_label_work_process_with_qc_bad_items", DefectTypeInfo.class);

		// 结合字典表转换不良类型名称
		return enrichWithBadItemNames(results.getMappedResults(), badItemRepository);
	}

	/**
	 * 查询指定线体指定日期的不良类型详细信息（包含不良点位信息）
	 * @param plId 线体ID
	 * @param date 指定日期
	 * @param isFirstPass 是否为一次直通（true: 一次直通失败, false: 复判后失败）
	 * @param mongoTemplate MongoDB操作模板
	 * @return 不良类型详细信息
	 */
	public static List<Map<String, Object>> getDefectTypeDetails(String plId, LocalDate date, boolean isFirstPass,
			MongoTemplate mongoTemplate) {
		// 计算日期范围（早班8点到次日早班8点）
		LocalDateTime startOfDay = date.atTime(8, 0, 0);
		LocalDateTime endOfDay = date.plusDays(1).atTime(8, 0, 0);

		Date startDate = Date.from(startOfDay.atZone(ZoneId.systemDefault()).toInstant());
		Date endDate = Date.from(endOfDay.atZone(ZoneId.systemDefault()).toInstant());

		// 构建查询条件
		Criteria matchCriteria = Criteria.where("plId")
			.is(plId)
			.and("finalWpCmpDate")
			.gte(startDate)
			.lte(endDate)
			.and("qcBadItems")
			.exists(true)
			.ne(null);

		if (isFirstPass) {
			// 一次直通失败
			matchCriteria.and("keyWpFirstResult").is("N");
		}
		else {
			// 复判后失败
			matchCriteria.and("keyWpFirstResult").is("Y").and("finalResult").is("N");
		}

		// 使用聚合管道获取详细信息
		Aggregation aggregation = Aggregation.newAggregation(
				// 匹配条件
				Aggregation.match(matchCriteria),
				// 展开QC不良项目数组
				Aggregation.unwind("qcBadItems"),
				// 按不良项目ID、不良点位和严重程度分组统计
				Aggregation.group("qcBadItems.badItemId", "qcBadItems.badPoint", "qcBadItems.fieldEx2")
					.count()
					.as("count")
					.first("qcBadItems.badItemId")
					.as("badItemId")
					.first("qcBadItems.badPoint")
					.as("badPoint")
					.first("qcBadItems.fieldEx2")
					.as("severity"),
				// 格式化输出
				Aggregation.project()
					.and("badItemId")
					.as("type")
					.and("badPoint")
					.as("point")
					.and("severity")
					.as("severity")
					.and("count")
					.as("count")
					.andExclude("_id"),
				// 按不良类型和数量排序
				Aggregation.sort(Sort.Direction.ASC, "type").and(Sort.Direction.DESC, "count"));

		@SuppressWarnings("rawtypes")
		AggregationResults<Map> results = mongoTemplate.aggregate(aggregation,
				"t_mes_label_work_process_with_qc_bad_items", Map.class);

		return results.getMappedResults().stream().map(result -> {
			@SuppressWarnings("unchecked")
			Map<String, Object> typedResult = (Map<String, Object>) result;
			return typedResult;
		}).collect(Collectors.toList());
	}

	/**
	 * 结合字典表为不良类型信息添加名称
	 * @param defectTypes 不良类型信息列表
	 * @param badItemRepository 不良项目Repository
	 * @return 包含名称的不良类型信息列表
	 */
	private static List<DefectTypeInfo> enrichWithBadItemNames(List<DefectTypeInfo> defectTypes,
			BadItemRepository badItemRepository) {
		if (defectTypes == null || defectTypes.isEmpty()) {
			return defectTypes;
		}

		// 提取所有不良类型ID
		List<String> badItemIds = defectTypes.stream().map(DefectTypeInfo::getType).collect(Collectors.toList());

		// 使用repository查询字典表获取名称映射
		List<BadItemDoc> badItems = badItemRepository.findByBadItemIdIn(badItemIds);

		// 创建ID到名称的映射
		Map<String, String> idToNameMap = badItems.stream()
			.collect(Collectors.toMap(BadItemDoc::getBadItemId, BadItemDoc::getBadItemName,
					(existing, replacement) -> existing // 如果有重复，保留第一个
			));

		// 为每个不良类型设置名称
		return defectTypes.stream().map(defectType -> {
			String typeName = idToNameMap.getOrDefault(defectType.getType(), defectType.getType());
			defectType.setTypeName(typeName);
			return defectType;
		}).collect(Collectors.toList());
	}

}
