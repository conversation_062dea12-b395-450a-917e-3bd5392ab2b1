package com.github.cret.web.oee.repository;

import java.util.Optional;

import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.stereotype.Repository;

import com.github.cret.web.oee.document.feedback.AnomaliesClassification;

@Repository
public interface AnomaliesClassificationRepository extends MongoRepository<AnomaliesClassification, String> {

	/**
	 * 根据线体编码和异常编码查找唯一的异常配置
	 * @param lineCode 线体编码
	 * @param anomaliesCode 异常编码
	 * @return 异常配置
	 */
	Optional<AnomaliesClassification> findByLineCodeAndAnomaliesCode(String lineCode, String anomaliesCode);

	/**
	 * 根据线体编码和异常编码和启用状态查找唯一的异常配置
	 * @param lineCode 线体编码
	 * @param anomaliesCode 异常编码
	 * @param enable 启用状态
	 * @return 异常配置
	 */
	Optional<AnomaliesClassification> findByLineCodeAndAnomaliesCodeAndEnable(String lineCode, String anomaliesCode,
			Boolean enable);

}
