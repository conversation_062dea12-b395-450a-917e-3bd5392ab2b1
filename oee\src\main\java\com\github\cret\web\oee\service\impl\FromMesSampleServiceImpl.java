package com.github.cret.web.oee.service.impl;

import java.util.List;

import org.springframework.stereotype.Service;

import com.github.cret.web.oee.document.mes.FromMesSample;
import com.github.cret.web.oee.repository.FromMesSampleRepository;
import com.github.cret.web.oee.service.FromMesSampleService;

@Service
public class FromMesSampleServiceImpl implements FromMesSampleService {

	private final FromMesSampleRepository mesSampleRepository;

	public FromMesSampleServiceImpl(FromMesSampleRepository mesSampleRepository) {
		this.mesSampleRepository = mesSampleRepository;
	}

	@Override
	public List<FromMesSample> findAllByLbIdIn(List<String> lbIds) {
		return mesSampleRepository.findAllByLbIdIn(lbIds);
	}

	@Override
	public void saveAll(List<FromMesSample> samples) {
		mesSampleRepository.saveAll(samples);
	}

}
