package com.github.cret.web.oee.service.impl;

import java.util.List;

import org.springframework.stereotype.Service;

import com.github.cret.web.oee.document.Workshop;
import com.github.cret.web.oee.repository.WorkshopRepository;
import com.github.cret.web.oee.service.WorkshopService;

@Service
public class WorkshopServiceImpl implements WorkshopService {

	private final WorkshopRepository repository;

	public WorkshopServiceImpl(WorkshopRepository repository) {
		this.repository = repository;
	}

	@Override
	public List<Workshop> getList() {
		return repository.findAll();
	}

	@Override
	public Workshop saveWorkshop(Workshop workshop) {
		return repository.save(workshop);
	}

	@Override
	public void deleteWorkshop(String id) {
		repository.deleteById(id);
	}

	@Override
	public Workshop getWorkshopById(String id) {
		return repository.findById(id).orElse(null);
	}

	@Override
	public Workshop findByCode(String code) {
		return repository.findByCode(code);
	}

}
