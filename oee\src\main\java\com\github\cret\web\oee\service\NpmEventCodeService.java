package com.github.cret.web.oee.service;

import java.util.List;

import com.github.cret.web.common.domain.PageList;
import com.github.cret.web.common.domain.PageableParam;
import com.github.cret.web.oee.document.event.NpmEventCode;
import com.github.cret.web.oee.enums.EventType;

/**
 * 松下贴片机事件代码服务接口
 */
public interface NpmEventCodeService {

	// 保存
	NpmEventCode save(NpmEventCode npmEventCode);

	// 查询所有
	List<NpmEventCode> findAll();

	// 根据ID查询
	NpmEventCode findById(String id);

	// 分页查询
	PageList<NpmEventCode> page(PageableParam<NpmEventCode> param);

	// 更新
	NpmEventCode update(NpmEventCode npmEventCode);

	// 根据ID删除
	void deleteById(String id);

	// 自定义查询方法
	List<NpmEventCode> findByMainCode(String mainCode);

	// 根据事件类型查询
	List<NpmEventCode> findByEventType(EventType eventType);

}