package com.github.cret.web.security.controller;

import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import com.github.cret.web.common.domain.PageList;
import com.github.cret.web.common.domain.PageableParam;
import com.github.cret.web.security.domain.ChangePasswordRequest;
import com.github.cret.web.security.domain.UserCreate;
import com.github.cret.web.security.domain.UserQuery;
import com.github.cret.web.security.domain.UserUpdate;
import com.github.cret.web.security.domain.UserView;
import com.github.cret.web.security.service.UserService;

@RestController
@RequestMapping("/user")
public class UserController {

	private final UserService userService;

	public UserController(UserService userService) {
		this.userService = userService;
	}

	@PostMapping("/create")
	@ResponseBody
	@PreAuthorize(value = "hasAuthority('ADMIN')")
	void create(@RequestBody UserCreate userCreate) {
		userService.create(userCreate);
	}

	@PostMapping("/update/{id}")
	@PreAuthorize(value = "hasAuthority('ADMIN')")
	void update(@PathVariable("id") String id, @RequestBody UserUpdate userUpdate) {
		userService.update(id, userUpdate);
	}

	@PostMapping("/changePassword")
	public Boolean changePassword(@RequestBody ChangePasswordRequest request) {
		return userService.changePassword(request.getOldPassword(), request.getNewPassword());
	}

	@PostMapping("/page")
	public PageList<UserView> page(@RequestBody PageableParam<UserQuery> param) {
		return userService.page(param);
	}

	@DeleteMapping("/{id}")
	@PreAuthorize(value = "hasAuthority('ADMIN')")
	public void delete(@PathVariable("id") String id) {
		userService.delete(id);
	}

	@GetMapping("/{id}")
	public UserView get(@PathVariable("id") String id) {
		return userService.get(id);
	}

}
