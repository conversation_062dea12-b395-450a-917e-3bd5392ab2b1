package com.github.cret.web.oee.domain.wx;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

public class WxUserListRes implements Serializable {

	@Serial
	private static final long serialVersionUID = 1L;

	private int errcode;

	private String errmsg;

	private List<WxUser> userlist;

	public int getErrcode() {
		return errcode;
	}

	public void setErrcode(int errcode) {
		this.errcode = errcode;
	}

	public String getErrmsg() {
		return errmsg;
	}

	public void setErrmsg(String errmsg) {
		this.errmsg = errmsg;
	}

	public List<WxUser> getUserlist() {
		return userlist;
	}

	public void setUserlist(List<WxUser> userlist) {
		this.userlist = userlist;
	}

}
