package com.github.cret.web.oee.domain.analyze;

/**
 * 实际生产数量分组, 如果AOI有拼板数则设置拼板数
 */
public class AoiProductionGroup {

	// 产品名称
	String productModel;

	// 实际生产数量
	Integer actualProduction;

	// 拼板数
	Integer flatNum;

	// 需要计算拼板
	Boolean needFlat;

	public String getProductModel() {
		return productModel;
	}

	public void setProductModel(String productModel) {
		this.productModel = productModel;
	}

	public Integer getActualProduction() {
		return actualProduction;
	}

	public void setActualProduction(Integer actualProduction) {
		this.actualProduction = actualProduction;
	}

	public Integer getFlatNum() {
		return flatNum;
	}

	public void setFlatNum(Integer flatNum) {
		this.flatNum = flatNum;
	}

	public Boolean getNeedFlat() {
		return needFlat;
	}

	public void setNeedFlat(Boolean needFlat) {
		this.needFlat = needFlat;
	}

}
