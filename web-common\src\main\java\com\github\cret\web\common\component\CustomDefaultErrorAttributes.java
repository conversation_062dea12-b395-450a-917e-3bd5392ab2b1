package com.github.cret.web.common.component;

import com.github.cret.web.common.enumerate.SysErrEnum;
import org.springframework.boot.web.error.ErrorAttributeOptions;
import org.springframework.boot.web.servlet.error.DefaultErrorAttributes;
import org.springframework.web.context.request.WebRequest;

import java.util.HashMap;
import java.util.Map;

public class CustomDefaultErrorAttributes extends DefaultErrorAttributes {

	@Override
	public Map<String, Object> getErrorAttributes(WebRequest webRequest, ErrorAttributeOptions options) {
		Map<String, Object> errorMap = super.getErrorAttributes(webRequest, options);
		Map<String, Object> errorAttributes = new HashMap<>();
		SysErrEnum unknownErr = SysErrEnum.UNKNOWN_ERROR;
		errorAttributes.put("code", unknownErr.getCode());
		errorAttributes.put("msg", errorMap.get("message"));
		return errorAttributes;
	}

}
