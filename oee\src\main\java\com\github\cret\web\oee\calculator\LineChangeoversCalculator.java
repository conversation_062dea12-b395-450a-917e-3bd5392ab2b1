package com.github.cret.web.oee.calculator;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;

import org.bson.Document;
import org.springframework.data.mongodb.core.MongoTemplate;

import com.github.cret.web.oee.document.Device;
import com.github.cret.web.oee.domain.analyze.AnalyzeQuery;
import com.github.cret.web.oee.domain.analyze.LineChangeoverBlock;
import com.github.cret.web.oee.domain.analyze.LineChangeoverInfo;
import com.github.cret.web.oee.utils.OeeUtil;

/**
 * 换线信息计算器
 */
public class LineChangeoversCalculator {

	/**
	 * 获取换线信息
	 * @param primarySmtDevices
	 * @param query
	 * @param mongoTemplate
	 * @return
	 */
	public static LineChangeoverInfo getLineChangeoverInfo(Map<String, Device> primarySmtDevices, AnalyzeQuery query,
			MongoTemplate mongoTemplate) {
		if (primarySmtDevices == null || primarySmtDevices.isEmpty()) {
			throw new IllegalArgumentException("未找到设备");
		}
		Device device = primarySmtDevices.values().iterator().next();
		return switch (device.getType()) {
			case smt_npm_reporter -> getNpmSmtLineChangeoverInfo(device, query, mongoTemplate);
			case smt_npm_reporter2 -> getNpmSmt2LineChangeOverInfo(primarySmtDevices, query, mongoTemplate);
			case smt_samsung -> getSamsungLineChangeoverInfo(device, query, mongoTemplate);
			case smt_yamaha -> getYamahaLineChangeoverInfo(device, query, mongoTemplate);
			default -> throw new UnsupportedOperationException("设备类型 " + device.getType() + " 未实现换线次数查询");
		};
	}

	/**
	 * 获取松下贴片机的换线信息
	 * @param device
	 * @param query
	 * @param mongoTemplate
	 * @return
	 */
	private static LineChangeoverInfo getNpmSmtLineChangeoverInfo(Device device, AnalyzeQuery query,
			MongoTemplate mongoTemplate) {
		// 获取生产日志集合
		String collectionName = OeeUtil.getProductionLogCollection(device.getCode());
		List<Document> pipeline = new ArrayList<>();

		// 1. 匹配时间范围和产品型号（如果有）
		Document matchDoc = new Document("time",
				new Document("$gte", query.getStartTime()).append("$lte", query.getEndTime()));

		// 如果指定了产品型号，添加产品型号过滤条件
		if (query.getProductModel() != null && !query.getProductModel().isEmpty()) {
			matchDoc.append("productmodel", query.getProductModel());
		}
		pipeline.add(new Document("$match", matchDoc));

		// 2. 添加排序
		pipeline.add(new Document("$sort", new Document("time", 1)));

		// 3. 添加窗口操作以获取前一个产品型号和计数
		Document windowFields = new Document("$setWindowFields",
				new Document().append("partitionBy", null)
					.append("sortBy", new Document("time", 1).append("totaltime", -1))
					.append("output",
							new Document()
								.append("prevproductmodel",
										new Document("$shift",
												new Document("output", "$productmodel").append("by", -1)))
								.append("prevcount", new Document("$shift",
										new Document("output", "$raw.count.board").append("by", -1)))));
		pipeline.add(windowFields);

		// 4. 移除前一个计数为null的记录
		pipeline.add(new Document("$match", new Document("prevcount", new Document("$ne", null))));

		// 5. 添加计算字段C和isDifferentProduct
		pipeline.add(new Document("$addFields",
				new Document()
					.append("C", new Document("$cond",
							new Document("if", new Document("$gt", Arrays.asList("$raw.count.board", "$prevcount")))
								.append("then",
										new Document("$subtract", Arrays.asList("$raw.count.board", "$prevcount")))
								.append("else", 0)))
					.append("isDifferentProduct",
							new Document("$ne", Arrays.asList("$productmodel", "$prevproductmodel")))));

		// 6. 添加分组标识符
		Document groupIdWindow = new Document("$setWindowFields", new Document().append("partitionBy", null)
			.append("sortBy", new Document("time", 1).append("totaltime", -1))
			.append("output", new Document().append("groupId",
					new Document("$sum",
							new Document("$cond", Arrays.asList(new Document("$ne", Arrays.asList("$C", 0)), 1, 0)))
						.append("window", new Document("documents", Arrays.asList("unbounded", "current"))))));
		pipeline.add(groupIdWindow);

		// 7. 筛选C为0的记录
		pipeline.add(new Document("$match", new Document("C", 0)));

		// 8. 按分组标识符分组
		pipeline.add(new Document("$group",
				new Document().append("_id", "$groupId")
					.append("firstDoc", new Document("$first", "$$ROOT"))
					.append("lastDoc", new Document("$last", "$$ROOT"))
					.append("allC", new Document("$push", "$C"))
					.append("hasLineChange", new Document("$push", "$isDifferentProduct"))
					.append("count", new Document("$sum", 1))));

		// 9. 筛选有效的换线期间
		pipeline.add(new Document("$match",
				new Document("$expr",
						new Document("$and",
								Arrays.asList(new Document("$eq", Arrays.asList(new Document("$max", "$allC"), 0)),
										new Document("$anyElementTrue", "$hasLineChange"))))));

		// 10. 计算时间差和其他差值
		pipeline
			.add(new Document("$addFields", new Document()
				.append("difference",
						new Document("$subtract",
								Arrays.asList("$lastDoc.raw.count.board", "$firstDoc.raw.count.board")))
				.append("timeDifference", new Document("$subtract", Arrays.asList("$lastDoc.time", "$firstDoc.time")))
				.append("startTime", "$firstDoc.time")
				.append("endTime", "$lastDoc.time")));

		// 11. 最终汇总
		pipeline.add(new Document("$group",
				new Document().append("_id", null)
					.append("totalDifference", new Document("$sum", "$difference"))
					.append("totalTimeDifference", new Document("$sum", "$timeDifference"))
					.append("blockCount", new Document("$sum", 1))
					.append("blocks",
							new Document("$push",
									new Document().append("groupId", "$_id")
										.append("startTime", "$startTime")
										.append("endTime", "$endTime")
										.append("timeDifference", "$timeDifference")
										.append("difference", "$difference")))));

		// 12. 格式化输出
		pipeline.add(new Document("$project",
				new Document().append("_id", 0)
					.append("totalDifference", 1)
					.append("totalTimeDifference", new Document("$divide", Arrays.asList("$totalTimeDifference", 1000)))
					.append("blockCount", 1)
					.append("blocks", 1)));

		// 执行聚合查询
		Document result = mongoTemplate.getCollection(collectionName).aggregate(pipeline).first();

		// 如果查询结果为空，返回一个空的 LineChangeoverInfo
		if (result == null) {
			return new LineChangeoverInfo();
		}

		LineChangeoverInfo lineChangeoverInfo = new LineChangeoverInfo();
		lineChangeoverInfo.setChangeoverTime((long) Math.floor(result.getDouble("totalTimeDifference")));
		lineChangeoverInfo.setChangeoverNum(result.getInteger("blockCount") + 1);

		// 解析 blocks
		List<LineChangeoverBlock> blocks = new ArrayList<>();
		for (Document blockDoc : result.getList("blocks", Document.class)) {
			LineChangeoverBlock block = new LineChangeoverBlock();
			block.setGroupId(blockDoc.getInteger("groupId"));
			block.setStartTime(blockDoc.getDate("startTime"));
			block.setEndTime(blockDoc.getDate("endTime"));
			block.setTimeDifference(blockDoc.getLong("timeDifference"));
			block.setDifference(blockDoc.getInteger("difference"));
			blocks.add(block);
		}
		lineChangeoverInfo.setBlocks(blocks);

		return lineChangeoverInfo;
	}

	/**
	 * 获取双轨的换线信息
	 * @param primarySmtDevices
	 * @param query
	 * @param mongoTemplate
	 * @return
	 */
	public static LineChangeoverInfo getNpmSmt2LineChangeOverInfo(Map<String, Device> primarySmtDevices,
			AnalyzeQuery query, MongoTemplate mongoTemplate) {
		LineChangeoverInfo result = new LineChangeoverInfo();

		// 处理空设备Map的情况
		if (primarySmtDevices == null || primarySmtDevices.isEmpty()) {
			return result;
		}

		// 存储所有设备的换线时间块（保持设备顺序）
		List<List<LineChangeoverBlock>> allDeviceBlocks = new ArrayList<>();

		// 遍历主设备（保持Map的插入顺序）
		for (Device device : primarySmtDevices.values()) {
			// 获取每个设备的换线信息
			LineChangeoverInfo info = getNpmSmtLineChangeoverInfo(device, query, mongoTemplate);

			// 任一设备没有换线块时直接返回空结果
			if (info.getBlocks() == null || info.getBlocks().isEmpty()) {
				return new LineChangeoverInfo();
			}
			allDeviceBlocks.add(info.getBlocks());
		}

		// 初始化统计值
		long totalIntersectionTime = 0;
		int changeoverNum = 0;

		// 以第一个设备的换线块为基准进行比对
		List<LineChangeoverBlock> firstDeviceBlocks = allDeviceBlocks.get(0);

		// 遍历基准设备的所有换线块
		blockLoop: for (LineChangeoverBlock baseBlock : firstDeviceBlocks) {
			// 当前块的初始时间窗口
			Date windowStart = baseBlock.getStartTime();
			Date windowEnd = baseBlock.getEndTime();

			// 与其他设备的换线块进行比对
			for (int i = 1; i < allDeviceBlocks.size(); i++) {
				List<LineChangeoverBlock> compareBlocks = allDeviceBlocks.get(i);
				boolean hasOverlap = false;

				// 在对比设备中寻找与当前时间窗口有交集的块
				for (LineChangeoverBlock compareBlock : compareBlocks) {
					Date overlapStart = windowStart.after(compareBlock.getStartTime()) ? windowStart
							: compareBlock.getStartTime();

					Date overlapEnd = windowEnd.before(compareBlock.getEndTime()) ? windowEnd
							: compareBlock.getEndTime();

					// 发现有效重叠区域
					if (overlapStart.before(overlapEnd)) {
						// 收缩时间窗口到重叠区域
						windowStart = overlapStart;
						windowEnd = overlapEnd;
						hasOverlap = true;
						break;
					}
				}

				// 任意设备没有重叠块则跳过该基准块
				if (!hasOverlap) {
					continue blockLoop;
				}
			}

			// 所有设备都有重叠时统计
			if (windowStart.before(windowEnd)) {
				// 累加换线时间（秒）
				long duration = (windowEnd.getTime() - windowStart.getTime()) / 1000;
				totalIntersectionTime += duration;

				// 换线次数+1
				changeoverNum++;
			}
		}

		// 设置最终结果
		result.setChangeoverNum(changeoverNum);
		result.setChangeoverTime(totalIntersectionTime);
		return result;
	}

	/**
	 * 获取三星贴片机的换线信息
	 * @param device 设备对象
	 * @param query 分析查询条件
	 * @param mongoTemplate MongoDB操作模板
	 * @return 换线信息对象
	 */
	private static LineChangeoverInfo getSamsungLineChangeoverInfo(Device device, AnalyzeQuery query,
			MongoTemplate mongoTemplate) {
		// 获取设备对应的生产日志集合名称
		String collectionName = OeeUtil.getProductionLogCollection(device.getCode());

		List<Document> pipeline = new ArrayList<>();

		// 1. 匹配阶段：根据时间范围和正常标志过滤文档
		pipeline.add(new Document("$match", new Document("time", new Document("$gte", query.getStartTime()) // 开始时间
			.append("$lte", query.getEndTime())) // 结束时间
			.append("normalflag", true))); // 正常生产标志

		// 2. 排序阶段：按时间升序排序
		pipeline.add(new Document("$sort", new Document("time", 1)));

		// 3. 分组阶段：将所有文档归并到一个数组中
		pipeline.add(new Document("$group", new Document("_id", null) // 合并所有文档
			.append("docs", new Document("$push", "$$ROOT")))); // 将完整文档推送到docs数组

		// 4. 计算阶段：使用reduce迭代处理换线信息
		pipeline
			.add(new Document("$project", new Document("result", new Document("$reduce", new Document("input", "$docs") // 输入数组
				// 初始值：记录前一个型号、结束时间、换线次数和时间
				.append("initialValue",
						new Document().append("previousProductModel", null)
							.append("previousEndtime", null)
							.append("changeoverNum", 0)
							.append("changeoverTime", 0))
				// 迭代逻辑
				.append("in", new Document()
					// 更新前一个产品型号
					.append("previousProductModel", "$$this.productmodel")
					// 更新前一个结束时间
					.append("previousEndtime", "$$this.endtime")
					// 换线次数计算：当型号不同且前一个型号不为空时计数+1
					.append("changeoverNum",
							new Document("$cond",
									new Document("if", new Document("$and", Arrays.asList(
											new Document("$ne",
													Arrays.asList("$$this.productmodel",
															"$$value.previousProductModel")),
											new Document("$ne", Arrays.asList("$$value.previousProductModel", null)))))
										.append("then", new Document("$add", Arrays.asList("$$value.changeoverNum", 1)))
										.append("else", "$$value.changeoverNum")))
					// 换线时间计算：计算时间差（单位秒）
					.append("changeoverTime",
							new Document("$cond",
									new Document("if",
											new Document("$and",
													Arrays.asList(
															new Document("$ne",
																	Arrays.asList("$$this.productmodel",
																			"$$value.previousProductModel")),
															new Document("$ne",
																	Arrays.asList("$$value.previousEndtime", null)))))
										.append("then",
												new Document("$add",
														Arrays.asList("$$value.changeoverTime",
																new Document("$divide", Arrays.asList( // 将毫秒转换为秒
																		new Document("$subtract",
																				Arrays.asList("$$this.starttime",
																						"$$value.previousEndtime")),
																		1000)))))
										.append("else", "$$value.changeoverTime"))))))));

		// 5. 投影阶段：输出最终结果字段
		pipeline.add(new Document("$project", new Document("_id", 0) // 排除_id字段
			.append("changeoverNum", "$result.changeoverNum") // 换线次数
			.append("changeoverTime", "$result.changeoverTime"))); // 换线总时间（秒）

		// 执行聚合管道查询
		Document result = mongoTemplate.getCollection(collectionName).aggregate(pipeline).first();

		// 构造返回结果对象
		LineChangeoverInfo info = new LineChangeoverInfo();
		if (result != null) {
			info.setChangeoverNum(result.getInteger("changeoverNum"));
			// 处理可能的空值情况，将秒转换为long类型
			Number changeoverTime = (Number) result.get("changeoverTime");
			info.setChangeoverTime(changeoverTime != null ? changeoverTime.longValue() : 0L);
		}

		return info;
	}

	/**
	 * 获取雅马哈贴片机的换线信息
	 * @param device
	 * @param query
	 * @param mongoTemplate
	 * @return
	 */
	/**
	 * 获取雅马哈设备的换线信息（换线次数和总换线时间）
	 * @param device 设备对象，包含设备唯一标识
	 * @param query 分析查询条件，包含时间范围等参数
	 * @param mongoTemplate MongoDB操作模板
	 * @return LineChangeoverInfo 换线信息对象
	 */
	private static LineChangeoverInfo getYamahaLineChangeoverInfo(Device device, AnalyzeQuery query,
			MongoTemplate mongoTemplate) {
		// 1. 获取设备对应的生产日志集合名称（根据设备编码动态获取）
		String collectionName = OeeUtil.getProductionLogCollection(device.getCode());

		List<Document> pipeline = new ArrayList<>();

		// 2. 第一阶段：筛选时间范围内的文档
		pipeline.add(new Document("$match", new Document("time", new Document("$gte", query.getStartTime()) // 起始时间
			.append("$lte", query.getEndTime())))); // 结束时间

		// 3. 第二阶段：按时间升序排序（为窗口函数提供有序数据）
		pipeline.add(new Document("$sort", new Document("time", 1))); // 1表示升序

		// 4. 第三阶段：窗口函数获取前一条记录的值
		pipeline.add(new Document("$setWindowFields", new Document("sortBy", new Document("time", 1)) // 按时间排序
			.append("output", new Document()
				// 获取前一条记录的productmodel字段（用于型号变化判断）
				.append("prevModel", new Document("$shift", new Document("output", "$productmodel") // 目标字段
					.append("by", -1) // 前移1位
					.append("default", null))) // 无前值时返回null
				// 获取前一条记录的time字段（用于时间差计算）
				.append("prevTime", new Document("$shift",
						new Document("output", "$time").append("by", -1).append("default", null))))));

		// 5. 第四阶段：计算换线时间和换线标记
		pipeline.add(new Document("$addFields", new Document()
			// 换线时间计算（型号变化时计算时间差）
			.append("overtime",
					new Document("$cond",
							Arrays.asList(new Document("$ne", Arrays.asList("$productmodel", "$prevModel")), // 型号不同
									new Document("$subtract", Arrays.asList("$productionstarttime", "$prevTime")), // 时间差（毫秒）
									0))) // 型号相同则时间为0
			// 换线次数标记（有效换线记为1）
			.append("isChangeover",
					new Document("$cond",
							Arrays.asList(new Document("$and",
									Arrays.asList(new Document("$ne", Arrays.asList("$productmodel", "$prevModel")), // 型号变化
											new Document("$ne", Arrays.asList("$prevModel", null)) // 排除第一条记录
									)), 1, // 满足条件记为1次换线
									0)))));

		// 6. 第五阶段：跳过第一条记录（无前值导致无效数据）
		pipeline.add(new Document("$skip", 1));

		// 7. 第六阶段：汇总总换线时间和次数
		pipeline.add(new Document("$group", new Document("_id", null) // 合并所有文档
			.append("totalOvertime", new Document("$sum", "$overtime")) // 总换线时间（毫秒）
			.append("changeoverCount", new Document("$sum", "$isChangeover")) // 换线次数
		));

		// 8. 第七阶段：单位转换（毫秒→秒）和结果格式化
		pipeline.add(new Document("$project", new Document("_id", 0) // 排除_id字段
			.append("totalOvertimeInSeconds", new Document("$divide", Arrays.asList("$totalOvertime", 1000))) // 转换为秒
			.append("changeoverCount", 1))); // 保留次数字段

		// 9. 执行聚合查询
		Document result = mongoTemplate.getCollection(collectionName).aggregate(pipeline).first();

		// 10. 构建返回对象
		LineChangeoverInfo info = new LineChangeoverInfo();
		if (result != null) {
			// 设置换线次数（直接取整型值）
			info.setChangeoverNum(result.getInteger("changeoverCount"));
			// 处理时间字段（可能为null，转换为Long类型）
			Double totalOvertimeInSeconds = result.getDouble("totalOvertimeInSeconds");
			info.setChangeoverTime(totalOvertimeInSeconds != null ? totalOvertimeInSeconds.longValue() : 0L);
		}

		return info;
	}

}
