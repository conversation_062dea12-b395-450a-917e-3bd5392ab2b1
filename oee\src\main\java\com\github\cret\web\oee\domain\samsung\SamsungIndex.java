package com.github.cret.web.oee.domain.samsung;

import org.springframework.data.mongodb.core.mapping.Field;

public record SamsungIndex(
		// 版本号
		@Field(name = "version") int version,

		// 开机时间（单位：秒）
		@Field(name = "powertime") int powerTime,

		// 放置时间
		@Field(name = "placetime") int placeTime,

		// 等待时间
		@Field(name = "waittime") int waitTime,

		// 运行时间 = 放置时间 + 等待时间 + 转移时间
		@Field(name = "runtime") int runTime,

		// 停止时间
		@Field(name = "stoptime") int stopTime,

		// 空闲时间
		@Field(name = "idletime") int idleTime,

		// 输入等待时间
		@Field(name = "inwaittime") // 注意保持字段名称的一致性
		int inWaitTime,

		// 输出等待时间
		@Field(name = "outwaittime") int outWaitTime,

		// 转移时间
		@Field(name = "transtime") int transTime,

		// 错误停止时间
		@Field(name = "wrongstoptime") int wrongStopTime,

		// 错误时间停止
		@Field(name = "errorstoptime") int errorStopTime,

		// 错误停止计数
		@Field(name = "wrongstopcount") int wrongStopCount,

		// 错误停止计数
		@Field(name = "errorstopcount") int errorStopCount,

		// 面板输入计数
		@Field(name = "panelincount") int panelInCount,

		// 面板输出计数
		@Field(name = "paneloutcount") int panelOutCount,

		// 面板计数
		@Field(name = "panelcount") int panelCount,

		// PCB数量
		@Field(name = "pcbcount") int pcbCount,

		// 错误PCB数量
		@Field(name = "errorpcbcount") int errorPcbCount,

		// 跳过的PCB数量
		@Field(name = "skippcbcount") int skipPcbCount,

		// 运行率
		@Field(name = "operationrate") double operationRate,

		// 放置率
		@Field(name = "placementrate") double placementRate,

		// 单个PCB的平均时间
		@Field(name = "meantimeperpcb") double meanTimePerPcb,

		// 实际每个PCB的时间
		@Field(name = "realtimeperpcb") double realTimePerPcb,

		// 每个PCB的转移时间
		@Field(name = "transfertimeperpcb") double transferTimePerPcb,

		// 穿过的数量
		@Field(name = "placecount") int placeCount) {
}