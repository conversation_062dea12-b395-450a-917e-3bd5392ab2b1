package com.github.cret.web.oee.domain.samsung;

import org.springframework.data.mongodb.core.mapping.Field;

public record StickerFeeder(
		// 送料器ID
		@Field(name = "id") String id,

		// 料号
		@Field(name = "materialnum") String materialNum,

		// 拾取数量
		@Field(name = "pickcount") int pickCount,

		// 错误数量
		@Field(name = "errorcount") int errorCount,

		// 成功数量
		@Field(name = "successcount") int successCount,

		// 跳过数量
		@Field(name = "skipcount") int skipCount,

		// 漏拾数量
		@Field(name = "misscount") int missCount,

		// 错误拾取数量
		@Field(name = "errorpickcount") int errorPickCount,

		// 位置信息
		@Field(name = "location") String location) {
}