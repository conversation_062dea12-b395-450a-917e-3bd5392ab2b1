package com.github.cret.web.oee.calculator;

import org.bson.Document;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.aggregation.Aggregation;
import org.springframework.data.mongodb.core.aggregation.AggregationResults;
import org.springframework.data.mongodb.core.query.Criteria;

import com.github.cret.web.oee.document.Device;
import com.github.cret.web.oee.domain.analyze.AnalyzeQuery;
import com.github.cret.web.oee.utils.OeeUtil;

/**
 * AOI不良品计数器类 该类提供了计算不同类型AOI设备的不良品数量的功能
 */
public class AoiDefectCountCalculator {

	/**
	 * 获取AOI不良品数 根据设备类型，选择相应的方法计算不良品数
	 * @param device 设备对象
	 * @param query 查询条件对象
	 * @param mongoTemplate MongoDB操作模板
	 * @return 不良品数量
	 */
	public static Integer getAoiDefectCount(Device device, AnalyzeQuery query, MongoTemplate mongoTemplate) {
		if (device == null || device.getType() == null) {
			throw new IllegalArgumentException("设备类型不能为空");
		}

		switch (device.getType()) {
			case aoi_yamaha:
				return getYamahaAoiDefectCount(device, query, mongoTemplate);
			case aoi_viscom:
				return getViscomAoiDefectCount(device, query, mongoTemplate);
			case aoi_jutze:
				return getJutzeAoiDefectCount(device, query, mongoTemplate);
			case aoi_delu:
				return getDeluAoiDefectCount(device, query, mongoTemplate);
			default:
				throw new UnsupportedOperationException("设备类型 " + device.getType() + " 未实现不良品数查询");
		}
	}

	/**
	 * 计算雅马哈AOI的不良品数
	 * @param device 设备对象
	 * @param query 查询条件对象
	 * @param mongoTemplate MongoDB操作模板
	 * @return 不良品数量
	 */
	public static Integer getYamahaAoiDefectCount(Device device, AnalyzeQuery query, MongoTemplate mongoTemplate) {
		// 动态获取集合名称（根据设备编码）
		String collectionName = OeeUtil.getProductionLogCollection(device.getCode());

		// 构建基础匹配条件
		Criteria matchCriteria = Criteria.where("time")
			.gte(query.getStartTime())
			.lte(query.getEndTime())
			.and("result")
			.is("ng")
			.and("foldertype")
			.is("NG");

		// 构建聚合管道
		Aggregation aggregation = Aggregation.newAggregation(Aggregation.match(matchCriteria),
				// 匹配条件
				Aggregation.match(matchCriteria),
				// 展开testrecord数组
				Aggregation.unwind("raw"),
				// 按文档ID和softwareversion1去重
				Aggregation.group("_id", "raw.softwareversion1").first("raw.defecttype").as("description"),
				// 按description分组统计
				Aggregation.group().count().as("count"));

		// 执行聚合查询
		AggregationResults<Document> results = mongoTemplate.aggregate(aggregation, collectionName, Document.class);

		// 处理查询结果
		Document result = results.getUniqueMappedResult();
		return result != null ? result.getInteger("count") : 0;
	}

	/**
	 * 计算维视AOI的不良品数
	 * @param device 设备对象
	 * @param query 查询条件对象
	 * @param mongoTemplate MongoDB操作模板
	 * @return 不良品数量
	 */
	public static Integer getViscomAoiDefectCount(Device device, AnalyzeQuery query, MongoTemplate mongoTemplate) {
		// 获取生产日志集合名称
		String collectionName = OeeUtil.getProductionLogCollection(device.getCode());

		// 构建基础匹配条件
		Criteria matchCriteria = Criteria.where("time")
			.gte(query.getStartTime())
			.lte(query.getEndTime())
			.and("result")
			.is("1")
			.and("foldertype")
			.is(""); // 只查询不良品记录

		// 如果指定了产品型号，添加产品型号过滤条件
		if (query.getProductModel() != null && !query.getProductModel().isEmpty()) {
			matchCriteria.and("productmodel").is(query.getProductModel());
		}

		// 使用聚合管道统计不良品数
		Aggregation aggregation = Aggregation.newAggregation(
				// 匹配条件
				Aggregation.match(matchCriteria),
				// 展开testrecord数组
				Aggregation.unwind("testrecord"),
				// 按文档ID和boardnumber去重
				Aggregation.group("_id", "testrecord.boardnumber").first("testrecord.description").as("description"),
				// 计算总数
				Aggregation.group().count().as("count"));

		// 执行聚合查询
		AggregationResults<Document> results = mongoTemplate.aggregate(aggregation, collectionName, Document.class);

		// 处理查询结果
		Document result = results.getUniqueMappedResult();
		return result != null ? result.getInteger("count") : 0;
	}

	/**
	 * 计算矩子AOI不良品数
	 */
	public static Integer getJutzeAoiDefectCount(Device device, AnalyzeQuery query, MongoTemplate mongoTemplate) {
		// 获取动态集合名称（根据设备编号）
		String collectionName = OeeUtil.getProductionLogCollection(device.getCode());

		// 构建基础匹配条件
		Criteria matchCriteria = Criteria.where("time")
			.gte(query.getStartTime())
			.lte(query.getEndTime())
			.and("result")
			.is("FAIL");

		// 可选的产品型号过滤
		if (query.getProductModel() != null && !query.getProductModel().isEmpty()) {
			matchCriteria.and("productmodel").is(query.getProductModel());
		}

		// 构建聚合管道
		Aggregation aggregation = Aggregation.newAggregation(Aggregation.match(matchCriteria), // 匹配条件
				Aggregation.unwind("testrecord"), // 展开测试记录
				// 第一阶段分组：按序列号去重
				Aggregation.group("_id", "testrecord.serialnumber").first("testrecord.errortype").as("errortype"),
				// 按数量降序排序
				Aggregation.group().count().as("count"));

		// 执行查询
		AggregationResults<Document> results = mongoTemplate.aggregate(aggregation, collectionName, Document.class);

		if (!results.iterator().hasNext()) {
			return 0;
		}

		Document result = results.iterator().next();
		Integer count = result.getInteger("count");
		return count != null ? count : 0;
	}

	public static Integer getDeluAoiDefectCount(Device device, AnalyzeQuery query, MongoTemplate mongoTemplate) {
		// 获取动态集合名称（根据设备编号）
		String collectionName = OeeUtil.getProductionLogCollection(device.getCode());

		// 构建基础匹配条件
		Criteria matchCriteria = Criteria.where("time")
			.gte(query.getStartTime())
			.lte(query.getEndTime())
			.and("firstresult")
			.is("FAIL");

		// 可选的产品型号过滤
		if (query.getProductModel() != null && !query.getProductModel().isEmpty()) {
			matchCriteria.and("productmodel").is(query.getProductModel());
		}

		// 构建聚合管道
		Aggregation aggregation = Aggregation.newAggregation(Aggregation.match(matchCriteria), // 匹配条件
				Aggregation.unwind("testrecord"), // 展开测试记录
				// 第一阶段分组：按序列号去重
				Aggregation.group("_id", "testrecord.serialnumber").first("testrecord.errortype").as("errortype"),
				// 按数量降序排序
				Aggregation.group().count().as("count"));

		// 执行查询
		AggregationResults<Document> results = mongoTemplate.aggregate(aggregation, collectionName, Document.class);

		if (!results.iterator().hasNext()) {
			return 0;
		}

		Document result = results.iterator().next();
		Integer count = result.getInteger("count");
		return count != null ? count : 0;
	}

}