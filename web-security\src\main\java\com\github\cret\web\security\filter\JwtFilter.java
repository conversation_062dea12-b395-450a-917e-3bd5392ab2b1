package com.github.cret.web.security.filter;

import com.github.cret.web.security.domain.AuthUser;
import com.github.cret.web.security.service.TokenService;
import io.jsonwebtoken.JwtException;
import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.http.HttpHeaders;
import org.springframework.lang.NonNull;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.AuthorityUtils;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.web.authentication.WebAuthenticationDetailsSource;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import org.springframework.web.filter.OncePerRequestFilter;

import java.io.IOException;
import java.util.List;

@Component
public class JwtFilter extends OncePerRequestFilter {

	private final TokenService tokenService;

	public final String TOKEN_PREFIX = "Bearer ";

	public JwtFilter(TokenService tokenService) {
		this.tokenService = tokenService;

	}

	@Override
	protected void doFilterInternal(@NonNull HttpServletRequest request, @NonNull HttpServletResponse response,
			@NonNull FilterChain chain) throws ServletException, IOException {
		// 获取授权头并验证
		final String header = request.getHeader(HttpHeaders.AUTHORIZATION);
		if (!StringUtils.hasText(header) || !header.startsWith(TOKEN_PREFIX)) {
			chain.doFilter(request, response);
			return;
		}

		// 获取jwt令牌并验证
		try {
			String accessKey = header.replace(TOKEN_PREFIX, "");
			AuthUser authUser = tokenService.validateAndParse(accessKey);
			List<GrantedAuthority> authorities = AuthorityUtils.createAuthorityList(authUser.perms());

			UsernamePasswordAuthenticationToken authentication = new UsernamePasswordAuthenticationToken(authUser, null,
					authorities);

			authentication.setDetails(new WebAuthenticationDetailsSource().buildDetails(request));
			SecurityContextHolder.getContext().setAuthentication(authentication);
			chain.doFilter(request, response);
		}
		catch (JwtException exception) {
			logger.error("auth exception", exception);
			chain.doFilter(request, response);
		}

	}

}
