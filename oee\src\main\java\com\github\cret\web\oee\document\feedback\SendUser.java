package com.github.cret.web.oee.document.feedback;

import org.springframework.data.mongodb.core.mapping.Field;

public class SendUser {

	@Field(name = "send_user_id")
	private String sendUserId;

	@Field(name = "send_user_name")
	private String sendUserName;

	@Field(name = "send_user_type")
	private String sendUserType;

	public String getSendUserId() {
		return sendUserId;
	}

	public void setSendUserId(String sendUserId) {
		this.sendUserId = sendUserId;
	}

	public String getSendUserName() {
		return sendUserName;
	}

	public void setSendUserName(String sendUserName) {
		this.sendUserName = sendUserName;
	}

	public String getSendUserType() {
		return sendUserType;
	}

	public void setSendUserType(String sendUserType) {
		this.sendUserType = sendUserType;
	}

}
