package com.github.cret.web.oee.excel;

import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.read.listener.ReadListener;
import com.alibaba.excel.util.ListUtils;
import com.github.cret.web.oee.document.analyze.TheoreticalOutput;
import com.github.cret.web.oee.repository.TheoreticalOutputRepository;

public class TheoreticalOutputListener implements ReadListener<TheoreticalOutput> {

	private static final Logger log = LoggerFactory.getLogger(TheoreticalOutputListener.class);

	/**
	 * 每隔5条存储数据库，实际使用中可以100条，然后清理list ，方便内存回收
	 */
	private static final int BATCH_COUNT = 100;

	/**
	 * 缓存的数据
	 */
	private List<TheoreticalOutput> cachedDataList = ListUtils.newArrayListWithExpectedSize(BATCH_COUNT);

	private final TheoreticalOutputRepository theoreticalOutputRepository;

	public TheoreticalOutputListener(TheoreticalOutputRepository theoreticalOutputRepository) {
		this.theoreticalOutputRepository = theoreticalOutputRepository;
	}

	@Override
	public void invoke(TheoreticalOutput data, AnalysisContext context) {
		cachedDataList.add(data);
		// 达到BATCH_COUNT了，需要去存储一次数据库，防止数据几万条数据在内存，容易OOM
		if (cachedDataList.size() >= BATCH_COUNT) {
			saveData();
			// 存储完成清理 list
			cachedDataList = ListUtils.newArrayListWithExpectedSize(BATCH_COUNT);
		}
	}

	@Override
	public void doAfterAllAnalysed(AnalysisContext context) {
		// 解析完成后的操作
		saveData();
		log.info("所有数据解析完成！");
	}

	private void saveData() {
		log.info("{}条数据，开始存储数据库！", cachedDataList.size());
		theoreticalOutputRepository.saveAll(cachedDataList);
		log.info("存储数据库成功！");
	}

}