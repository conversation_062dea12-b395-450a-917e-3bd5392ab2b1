package com.github.cret.web.oee.controller;

import java.util.List;
import java.util.Map;
import java.util.Optional;

import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.github.cret.web.oee.document.BadItemDoc;
import com.github.cret.web.oee.service.BadItemService;

/**
 * 不良类型字典管理控制器
 */
@RestController
@RequestMapping("/bad-items")
public class BadItemController {

	private final BadItemService badItemService;

	public BadItemController(BadItemService badItemService) {
		this.badItemService = badItemService;
	}

	/**
	 * 获取所有不良项目信息
	 * @return 所有不良项目信息列表
	 */
	@GetMapping
	public List<BadItemDoc> getAllBadItems() {
		return badItemService.findAll();
	}

	/**
	 * 根据不良项目ID查找不良项目信息
	 * @param badItemId 不良项目ID
	 * @return 不良项目信息
	 */
	@GetMapping("/{badItemId}")
	public Optional<BadItemDoc> getBadItemById(@PathVariable String badItemId) {
		return badItemService.findByBadItemId(badItemId);
	}

	/**
	 * 根据不良类型ID查找不良项目信息
	 * @param badTypeId 不良类型ID
	 * @return 不良项目信息列表
	 */
	@GetMapping("/by-type/{badTypeId}")
	public List<BadItemDoc> getBadItemsByTypeId(@PathVariable String badTypeId) {
		return badItemService.findByBadTypeId(badTypeId);
	}

	/**
	 * 根据不良项目名称模糊查询
	 * @param name 不良项目名称关键字
	 * @return 不良项目信息列表
	 */
	@GetMapping("/search")
	public List<BadItemDoc> searchBadItemsByName(@RequestParam String name) {
		return badItemService.findByBadItemNameContaining(name);
	}

	/**
	 * 批量查询不良项目ID到名称的映射
	 * @param badItemIds 不良项目ID列表
	 * @return ID到名称的映射
	 */
	@PostMapping("/name-mapping")
	public Map<String, String> getBadItemNameMapping(@RequestBody List<String> badItemIds) {
		return badItemService.getBadItemIdToNameMap(badItemIds);
	}

	/**
	 * 创建新的不良项目信息
	 * @param badItem 不良项目信息
	 * @return 创建后的不良项目信息
	 */
	@PostMapping
	public BadItemDoc createBadItem(@RequestBody BadItemDoc badItem) {
		return badItemService.save(badItem);
	}

	/**
	 * 批量创建不良项目信息
	 * @param badItems 不良项目信息列表
	 * @return 创建后的不良项目信息列表
	 */
	@PostMapping("/batch")
	public List<BadItemDoc> createBadItems(@RequestBody List<BadItemDoc> badItems) {
		return badItemService.saveAll(badItems);
	}

	/**
	 * 更新不良项目信息
	 * @param badItemId 不良项目ID
	 * @param badItem 更新的不良项目信息
	 * @return 更新后的不良项目信息
	 */
	@PutMapping("/{badItemId}")
	public BadItemDoc updateBadItem(@PathVariable String badItemId, @RequestBody BadItemDoc badItem) {
		badItem.setBadItemId(badItemId);
		return badItemService.save(badItem);
	}

	/**
	 * 删除不良项目信息
	 * @param badItemId 不良项目ID
	 */
	@DeleteMapping("/{badItemId}")
	public void deleteBadItem(@PathVariable String badItemId) {
		badItemService.deleteByBadItemId(badItemId);
	}

	/**
	 * 检查不良项目ID是否存在
	 * @param badItemId 不良项目ID
	 * @return 是否存在
	 */
	@GetMapping("/{badItemId}/exists")
	public boolean checkBadItemExists(@PathVariable String badItemId) {
		return badItemService.existsByBadItemId(badItemId);
	}

	/**
	 * 获取不良类型统计信息
	 * @return 按不良类型ID分组的统计信息
	 */
	@GetMapping("/statistics")
	public Map<String, Long> getBadTypeStatistics() {
		return badItemService.getBadTypeStatistics();
	}

}
