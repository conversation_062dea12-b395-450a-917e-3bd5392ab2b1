package com.github.cret.web.oee.document.feedback;

import java.util.List;

import org.springframework.data.mongodb.core.mapping.Field;

public class ResponseConfig {

	@Field(name = "point_time")
	private Integer pointTime;

	@Field(name = "responder")
	private List<Responder> responders;

	@Field(name = "reporter")
	private List<Reporter> reporters;

	public Integer getPointTime() {
		return pointTime;
	}

	public void setPointTime(Integer pointTime) {
		this.pointTime = pointTime;
	}

	public List<Responder> getResponders() {
		return responders;
	}

	public void setResponders(List<Responder> responders) {
		this.responders = responders;
	}

	public List<Reporter> getReporters() {
		return reporters;
	}

	public void setReporters(List<Reporter> reporters) {
		this.reporters = reporters;
	}

}
