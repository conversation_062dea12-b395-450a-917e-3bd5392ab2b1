package com.github.cret.web.oee.config;

import java.net.URLEncoder;
import java.util.HashMap;
import java.util.Map;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import com.github.cret.web.oee.domain.request.FeedbackUrlParams;
import com.github.cret.web.oee.utils.UrlEncryptionUtil;

/**
 * 反馈相关配置类，用于存储和管理反馈处理相关的URL配置
 */
@Component
@ConfigurationProperties(prefix = "feedback")
public class FeedbackConfig {

	// 反馈处理基础URL
	private String handleBaseUrl = "http://localhost:3333/feedback/handle";

	// 仪表板基础URL
	private String dashboardBaseUrl = "http://************:3000/dashboard";

	// 默认仪表板路径
	private String defaultDashboardPath = "SMT1-1";

	// URL参数加密配置
	private boolean enableUrlEncryption = true;

	// URL参数加密密钥（Base64编码，256位AES密钥）
	// 注意：在生产环境中应该通过环境变量或配置文件设置此密钥
	// 这是一个有效的256位(32字节)AES密钥的Base64编码
	private String urlEncryptionKey = "YWJjZGVmZ2hpamtsbW5vcHFyc3R1dnd4eXoxMjM0NTY=";

	public String getHandleBaseUrl() {
		return handleBaseUrl;
	}

	public void setHandleBaseUrl(String handleBaseUrl) {
		this.handleBaseUrl = handleBaseUrl;
	}

	public String getDashboardBaseUrl() {
		return dashboardBaseUrl;
	}

	public void setDashboardBaseUrl(String dashboardBaseUrl) {
		this.dashboardBaseUrl = dashboardBaseUrl;
	}

	public String getDefaultDashboardPath() {
		return defaultDashboardPath;
	}

	public void setDefaultDashboardPath(String defaultDashboardPath) {
		this.defaultDashboardPath = defaultDashboardPath;
	}

	public boolean isEnableUrlEncryption() {
		return enableUrlEncryption;
	}

	public void setEnableUrlEncryption(boolean enableUrlEncryption) {
		this.enableUrlEncryption = enableUrlEncryption;
	}

	public String getUrlEncryptionKey() {
		return urlEncryptionKey;
	}

	public void setUrlEncryptionKey(String urlEncryptionKey) {
		this.urlEncryptionKey = urlEncryptionKey;
	}

	/**
	 * 构建反馈处理URL
	 * @param params 反馈URL构建参数
	 * @return 完整的反馈处理URL
	 */
	public String buildFeedbackUrl(FeedbackUrlParams params) {
		// 如果启用了URL加密且密钥有效，尝试加密参数
		if (isUrlEncryptionEnabled()) {
			try {
				Map<String, String> urlParams = createUrlParamsMap(params);
				String encryptedQuery = UrlEncryptionUtil.buildEncryptedQueryString(urlParams, urlEncryptionKey);
				return handleBaseUrl + encryptedQuery;
			}
			catch (Exception e) {
				// 加密失败时回退到明文参数
				return buildPlainUrl(params);
			}
		}
		else {
			// 未启用加密，使用明文参数
			return buildPlainUrl(params);
		}
	}

	/**
	 * 创建URL参数映射
	 * @param params 反馈URL参数对象
	 * @return 参数映射
	 */
	private Map<String, String> createUrlParamsMap(FeedbackUrlParams params) {
		Map<String, String> urlParams = new HashMap<>();
		urlParams.put("action", params.getAction());
		urlParams.put("triggerRecordId", params.getTriggerRecordId());
		urlParams.put("sendRecordId", params.getSendRecordId());
		urlParams.put("userId", params.getUserId());
		urlParams.put("userName", params.getUserName());
		return urlParams;
	}

	/**
	 * 构建明文参数URL
	 * @param params 反馈URL参数对象
	 * @return 明文参数URL
	 */
	private String buildPlainUrl(FeedbackUrlParams params) {
		StringBuilder urlBuilder = new StringBuilder(handleBaseUrl).append("?action=")
			.append(params.getAction())
			.append("&triggerRecordId=")
			.append(params.getTriggerRecordId())
			.append("&sendRecordId=")
			.append(params.getSendRecordId())
			.append("&userId=")
			.append(params.getUserId());

		if (params.getUserName() != null && !params.getUserName().trim().isEmpty()) {
			try {
				String encodedUserName = URLEncoder.encode(params.getUserName(), "UTF-8");
				urlBuilder.append("&userName=").append(encodedUserName);
			}
			catch (Exception e) {
				// 编码失败时跳过用户名参数
			}
		}

		return urlBuilder.toString();
	}

	/**
	 * 构建完整的仪表板URL
	 * @param dashboardPath 仪表板路径，如果为null则使用默认路径
	 * @return 完整的仪表板URL
	 */
	public String buildDashboardUrl(String dashboardPath) {
		String path = dashboardPath != null ? dashboardPath : defaultDashboardPath;
		return dashboardBaseUrl + "/" + path;
	}

	/**
	 * 构建默认仪表板URL
	 * @return 默认仪表板URL
	 */
	public String buildDefaultDashboardUrl() {
		return buildDashboardUrl(null);
	}

	/**
	 * 解密URL查询参数（供前端使用）
	 * @param encryptedData 加密的参数数据
	 * @return 解密后的参数Map
	 */
	public Map<String, String> decryptUrlParams(String encryptedData) {
		if (!enableUrlEncryption || !UrlEncryptionUtil.isValidKey(urlEncryptionKey)) {
			return new HashMap<>();
		}

		try {
			return UrlEncryptionUtil.decryptParams(encryptedData, urlEncryptionKey);
		}
		catch (Exception e) {
			// 解密失败，返回空Map
			return new HashMap<>();
		}
	}

	/**
	 * 检查是否启用了URL加密
	 * @return true表示启用了URL加密，false表示未启用
	 */
	public boolean isUrlEncryptionEnabled() {
		return enableUrlEncryption && UrlEncryptionUtil.isValidKey(urlEncryptionKey);
	}

}
