package com.github.cret.web.security.service.impl;

import java.util.HashSet;
import java.util.Optional;

import org.jetbrains.annotations.NotNull;
import org.springframework.data.domain.Example;
import org.springframework.data.domain.ExampleMatcher;
import org.springframework.data.domain.ExampleMatcher.GenericPropertyMatchers;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Service;

import com.github.cret.web.common.domain.PageList;
import com.github.cret.web.common.domain.PageableParam;
import com.github.cret.web.common.enumerate.SysErrEnum;
import com.github.cret.web.security.document.User;
import com.github.cret.web.security.domain.AuthUser;
import com.github.cret.web.security.domain.UserCreate;
import com.github.cret.web.security.domain.UserQuery;
import com.github.cret.web.security.domain.UserUpdate;
import com.github.cret.web.security.domain.UserView;
import com.github.cret.web.security.mapper.UserMapper;
import com.github.cret.web.security.repository.UserRepository;
import com.github.cret.web.security.service.DigestService;
import com.github.cret.web.security.service.UserService;
import com.github.cret.web.security.util.SecurityUtil;

@Service
public class UserServiceImpl implements UserService {

	private final UserRepository repository;

	private final UserMapper userMapper;

	private final MongoTemplate mongoTemplate;

	private final DigestService digestService;

	public UserServiceImpl(UserRepository repository, UserMapper userMapper, MongoTemplate mongoTemplate,
			DigestService digestService) {
		this.repository = repository;
		this.userMapper = userMapper;
		this.mongoTemplate = mongoTemplate;
		this.digestService = digestService;
	}

	@Override
	public void delete(String id) {
		repository.deleteById(id);

	}

	@Override
	public PageList<UserView> page(PageableParam<UserQuery> queryParam) {
		User user = userMapper.toEntity(queryParam.getSearchParams());
		Example<User> example = Example.of(user,
				ExampleMatcher.matching()
					.withIgnoreNullValues()
					.withMatcher("name", GenericPropertyMatchers.startsWith())
					.withMatcher("displayName", GenericPropertyMatchers.contains()));
		return PageList.fromPage(repository.findAll(example, queryParam.getPageRequest()).map(userMapper::toView));
	}

	@Override
	public void update(String id, UserUpdate userUpdate) {
		Update update = new Update();
		update.set("displayName", userUpdate.displayName());
		if (userUpdate.isAdmin() != null) {
			update.set("isAdmin", userUpdate.isAdmin());
		}
		update.set("avatar", userUpdate.avatar());
		update.set("perms", userUpdate.perms());
		update.set("roles", userUpdate.roles());
		mongoTemplate.updateFirst(Query.query(Criteria.where("_id").is(id)), update, User.class);
	}

	@Override
	public AuthUser findAuthUserByUsername(String username) {
		Optional<User> userOp = repository.findFirstByName(username);
		if (userOp.isEmpty()) {
			throw SysErrEnum.NOT_FOUND.exception();
		}
		else {
			return userMapper.toAuthUser(userOp.get());
		}
	}

	@Override
	public User create(@NotNull UserCreate userCreate) {
		if (repository.existsByName(userCreate.name())) {
			throw SysErrEnum.CONFLICT_KEY.exception("用户名已存在");
		}
		User user = userMapper.toEntity(userCreate);
		user.setPassword(digestService.digestPassword(userCreate.password()));
		user.setPerms(new HashSet<>());
		user.setRoles(new HashSet<>());
		user.setLocked(false);
		// 默认不是管理员
		user.setIsAdmin(false);
		return repository.save(user);
	}

	@Override
	public Boolean changePassword(String oldPassword, String newPassword) {
		// 获取当前用户
		AuthUser currentUser = SecurityUtil.getCurrentUser();
		if (currentUser == null) {
			throw SysErrEnum.UNAUTHORIZED.exception("用户未登录");
		}

		// 查找用户
		User user = repository.findFirstByName(currentUser.name())
			.orElseThrow(() -> SysErrEnum.NOT_FOUND.exception("用户不存在"));

		// 检查新密码是否与旧密码相同
		String oldPasswordHash = digestService.digestPassword(oldPassword);
		if (!oldPasswordHash.equals(user.getPassword())) {
			throw SysErrEnum.UNKNOWN_ERROR.exception("原密码错误");
		}

		user.setPassword(digestService.digestPassword(newPassword));
		repository.save(user);
		return true;
	}

	@Override
	public void lock(String id) {
		mongoTemplate.update(User.class).matching(Criteria.where("_id").is(id)).apply(Update.update("locked", true));
	}

	@Override
	public UserView get(String id) {
		return repository.findById(id).map(userMapper::toView).orElseThrow();
	}

}
