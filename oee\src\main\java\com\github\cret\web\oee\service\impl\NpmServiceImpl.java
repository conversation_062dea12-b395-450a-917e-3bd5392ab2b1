package com.github.cret.web.oee.service.impl;

import java.util.List;

import org.springframework.stereotype.Service;
import org.springframework.web.reactive.function.client.WebClient;

import com.github.cret.web.oee.domain.npm.LoginRes;
import com.github.cret.web.oee.domain.npm.ReportRes;
import com.github.cret.web.oee.service.NpmService;

import reactor.core.publisher.Mono;

@Service
public class NpmServiceImpl implements NpmService {

	private final WebClient webClient;

	public NpmServiceImpl(WebClient.Builder webClientBuilder) {
		this.webClient = webClientBuilder.baseUrl("http://localhost:8080").build();
	}

	@Override
	public Mono<LoginRes> getLogin() {
		return webClient.get().uri("/login").retrieve().bodyToMono(LoginRes.class);
	}

	@Override
	public Mono<List<ReportRes>> getReferProductManageReportAction() {
		return webClient.get()
			.uri("/ReferProductManageReportAction")
			.retrieve()
			.bodyToFlux(ReportRes.class)
			.collectList(); // 将流转换为列表
	}

	@Override
	public Mono<String> getReferProductManageReportDtlAction(String listindex) {
		return webClient.get()
			.uri(uriBuilder -> uriBuilder.path("/ReferProductManageReportDtlAction")
				.queryParam("listindex", listindex != null ? listindex : 0)
				.build())
			.retrieve()
			.bodyToMono(String.class);
	}

}
