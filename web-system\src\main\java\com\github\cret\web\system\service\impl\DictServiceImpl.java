package com.github.cret.web.system.service.impl;

import com.github.cret.web.common.domain.PageList;
import com.github.cret.web.common.domain.PageableParam;
import com.github.cret.web.system.document.Dict;
import com.github.cret.web.system.domain.DictCreate;
import com.github.cret.web.system.domain.DictItem;
import com.github.cret.web.system.domain.DictSimple;
import com.github.cret.web.system.domain.DictUpdate;
import com.github.cret.web.system.mapper.DictMapper;
import com.github.cret.web.system.repository.DictRepository;
import com.github.cret.web.system.service.DictService;
import org.springframework.data.domain.Example;
import org.springframework.data.domain.ExampleMatcher;
import org.springframework.data.domain.ExampleMatcher.GenericPropertyMatchers;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class DictServiceImpl implements DictService {

	private final DictRepository repository;

	private final DictMapper dictMapper;

	private final MongoTemplate mongoTemplate;

	public DictServiceImpl(DictRepository repository, DictMapper dictMapper, MongoTemplate mongoTemplate) {
		this.repository = repository;
		this.dictMapper = dictMapper;
		this.mongoTemplate = mongoTemplate;
	}

	@Override
	public Dict getByCode(String code) {
		return repository.findByCode(code).get();
	}

	@Override
	public PageList<DictSimple> page(PageableParam<DictSimple> param) {
		ExampleMatcher matcher = ExampleMatcher.matching()
			.withIgnoreNullValues()
			.withMatcher("id", GenericPropertyMatchers.exact())
			.withMatcher("name", GenericPropertyMatchers.startsWith())
			.withMatcher("code", GenericPropertyMatchers.exact());
		Example<Dict> example = Example.of(dictMapper.toEntity(param.getSearchParams()), matcher);
		return PageList.fromPage(repository.findAll(example, param.getPageRequest()).map(dictMapper::toSimple));
	}

	@Override
	public void create(DictCreate dictCreate) {
		repository.save(dictMapper.toEntity(dictCreate));
	}

	@Override
	public void delete(String id) {
		repository.deleteById(id);
	}

	@Override
	public Dict get(String id) {
		return repository.findById(id).get();
	}

	@Override
	public void update(String id, DictUpdate dictUpdate) {
		mongoTemplate.findAndModify(Query.query(Criteria.where("_id").is(id)),
				Update.update("name", dictUpdate.name()).set("itemList", dictUpdate.itemList()), Dict.class);
	}

	@Override
	public List<DictItem> listItem(String dictCode) {

		return repository.findByCode(dictCode).get().getItemList();
	}

}
