package com.github.cret.web.system.repository;

import java.util.Optional;

import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Repository;

import com.github.cret.web.system.document.Dict;

@Repository
public interface DictRepository extends MongoRepository<Dict, String> {

	Optional<Dict> findByCode(@NonNull String code);

}
