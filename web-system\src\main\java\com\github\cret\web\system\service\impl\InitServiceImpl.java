package com.github.cret.web.system.service.impl;

import java.util.List;
import java.util.Set;

import org.springframework.beans.factory.InitializingBean;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.github.cret.web.security.document.User;
import com.github.cret.web.security.domain.UserCreate;
import com.github.cret.web.security.service.UserService;
import com.github.cret.web.system.document.MenuItem;
import com.github.cret.web.system.service.InitService;

@Service
public class InitServiceImpl implements InitService, InitializingBean {

	private final MongoTemplate mongoTemplate;

	private final UserService userService;

	public InitServiceImpl(MongoTemplate mongoTemplate, UserService userService) {
		this.mongoTemplate = mongoTemplate;
		this.userService = userService;
	}

	@Override
	@Transactional
	public void afterPropertiesSet() throws Exception {
		init();
	}

	@Override
	public void init() {
		if (!mongoTemplate.collectionExists("sys_flag")) {
			initMenu();
			initAdmin();
			mongoTemplate.createCollection("sys_flag");
		}
	}

	private void initMenu() {
		MenuItem adminMenu = new MenuItem();
		adminMenu.setTitle("系统管理");
		adminMenu.setPath("/admin");
		adminMenu.setAccess("admin");
		MenuItem dictMenu = new MenuItem();
		dictMenu.setAccess("admin");
		dictMenu.setPath("/admin/dict");
		dictMenu.setTitle("字典管理");
		MenuItem menuMenu = new MenuItem();
		menuMenu.setTitle("菜单管理");
		menuMenu.setAccess("admin");
		menuMenu.setPath("/admin/menu");

		adminMenu = mongoTemplate.insert(adminMenu);
		dictMenu.setParentId(adminMenu.getId());
		menuMenu.setParentId(adminMenu.getId());

		List<MenuItem> menuItemList = List.of(dictMenu, menuMenu);
		mongoTemplate.insertAll(menuItemList);
	}

	private void initAdmin() {
		UserCreate userCreate = new UserCreate("admin", "管理员", "", "123456", true);
		User user = userService.create(userCreate);
		user.setPerms(Set.of("ADMIN"));
	}

}
