package com.github.cret.web.oee.service;

import java.util.List;

import com.github.cret.web.common.domain.PageList;
import com.github.cret.web.common.domain.PageableParam;
import com.github.cret.web.oee.document.analyze.TargetOee;

/**
 * 目标OEE服务
 */
public interface TargetOeeService {

	// 保存目标OEE
	TargetOee save(TargetOee targetOee);

	// 根据ID查询目标OEE
	TargetOee findById(String id);

	// 查询所有目标OEE
	List<TargetOee> findAll();

	// 根据ID删除目标OEE
	void deleteById(String id);

	// 分页查询目标OEE
	PageList<TargetOee> page(PageableParam<TargetOee> param);

	TargetOee getTargetOeeOrDefault();

}
