package com.github.cret.web.oee.document.mes;

import java.util.Date;

import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

/**
 * 标签工艺过程分析
 */
@Document("t_mes_label_work_process_analysis")
public class LabelWorkProcessAnalysis {

	@Id
	private String id;

	/**
	 * 标签ID
	 */
	@Field(name = "lbId")
	private String lbId;

	/**
	 * 线体ID
	 */
	@Field(name = "plId")
	private String plId;

	/**
	 * 制造订单号
	 */
	@Field(name = "mo")
	private String mo;

	/**
	 * 首次结果（所有工序的最小IS_PASS值）
	 */
	@Field(name = "firstResult")
	private String firstResult;

	/**
	 * 关键工序首次结果（仅考虑SMT-03和SMT-06工序的最小IS_PASS值）
	 */
	@Field(name = "keyWpFirstResult")
	private String keyWpFirstResult;

	/**
	 * 最终结果（按完成时间排序的最后一个IS_PASS值）
	 */
	@Field(name = "finalResult")
	private String finalResult;

	/**
	 * 最终工序完成时间
	 */
	@Field(name = "finalWpCmpDate")
	private Date finalWpCmpDate;

	/**
	 * 数据同步时间
	 */
	@Field(name = "syncTime")
	private Date syncTime;

	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	}

	public String getLbId() {
		return lbId;
	}

	public void setLbId(String lbId) {
		this.lbId = lbId;
	}

	public String getPlId() {
		return plId;
	}

	public void setPlId(String plId) {
		this.plId = plId;
	}

	public String getMo() {
		return mo;
	}

	public void setMo(String mo) {
		this.mo = mo;
	}

	public String getFirstResult() {
		return firstResult;
	}

	public void setFirstResult(String firstResult) {
		this.firstResult = firstResult;
	}

	public String getKeyWpFirstResult() {
		return keyWpFirstResult;
	}

	public void setKeyWpFirstResult(String keyWpFirstResult) {
		this.keyWpFirstResult = keyWpFirstResult;
	}

	public String getFinalResult() {
		return finalResult;
	}

	public void setFinalResult(String finalResult) {
		this.finalResult = finalResult;
	}

	public Date getFinalWpCmpDate() {
		return finalWpCmpDate;
	}

	public void setFinalWpCmpDate(Date finalWpCmpDate) {
		this.finalWpCmpDate = finalWpCmpDate;
	}

	public Date getSyncTime() {
		return syncTime;
	}

	public void setSyncTime(Date syncTime) {
		this.syncTime = syncTime;
	}

	@Override
	public String toString() {
		return "LabelWorkProcessAnalysis{" + "id='" + id + '\'' + ", lbId='" + lbId + '\'' + ", plId='" + plId + '\''
				+ ", mo='" + mo + '\'' + ", firstResult='" + firstResult + '\'' + ", keyWpFirstResult='"
				+ keyWpFirstResult + '\'' + ", finalResult='" + finalResult + '\'' + ", finalWpCmpDate="
				+ finalWpCmpDate + ", syncTime=" + syncTime + '}';
	}

}
