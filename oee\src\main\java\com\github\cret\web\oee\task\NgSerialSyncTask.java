package com.github.cret.web.oee.task;

import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.mongodb.core.BulkOperations;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import com.github.cret.web.oee.calculator.AoiNgCalculator;
import com.github.cret.web.oee.client.MesService;
import com.github.cret.web.oee.document.Device;
import com.github.cret.web.oee.document.mes.FromMesSample;
import com.github.cret.web.oee.document.mes.LabelLatestStatus;
import com.github.cret.web.oee.domain.analyze.AnalyzeQuery;
import com.github.cret.web.oee.enums.DeviceCategory;
import com.github.cret.web.oee.service.DeviceService;
import com.github.cret.web.oee.service.FromMesSampleService;
import com.github.cret.web.oee.service.LbwpService;

/**
 * NG流水号同步任务
 * <p>
 * 该任务用于同步本地系统中发现的NG（不良）流水号的状态， 与外部MES（制造执行系统）的主状态保持一致。
 * 确保最初标记为NG的流水号，如果后续被MES确认为PASS，则及时更新。
 * </p>
 */
@Service
public class NgSerialSyncTask {

	private static final Logger log = LoggerFactory.getLogger(NgSerialSyncTask.class);

	private final DeviceService deviceService;

	private final LbwpService lbwpService;

	private final MesService mesService;

	private final MongoTemplate mongoTemplate;

	private final FromMesSampleService mesSampleService;

	public NgSerialSyncTask(DeviceService deviceService, MongoTemplate mongoTemplate, LbwpService lbwpService,
			MesService mesService, FromMesSampleService mesSampleService) {
		this.deviceService = deviceService;
		this.mongoTemplate = mongoTemplate;
		this.lbwpService = lbwpService;
		this.mesService = mesService;
		this.mesSampleService = mesSampleService;
	}

	/**
	 * 手动触发NG流水号同步的主入口。
	 * @param query 分析的时间范围和条件。
	 */
	public void syncNgSerials(AnalyzeQuery query) {
		log.info("Starting manual NG serial sync for query: {}", query);
		executeSyncLogic(query);
	}

	/**
	 * 定时任务：每5分钟同步一次最近的NG流水号。
	 */
	// @Scheduled(fixedDelay = 300_000) // 300,000 毫秒 = 5 分钟
	// public void syncRecentNgSerials() {
	// log.info("Starting scheduled 5-minute NG serial sync...");
	// AnalyzeQuery query =
	// BuilderUtil.builder(AnalyzeQuery::new).with(AnalyzeQuery::generateTimeRange).build();
	// executeSyncLogic(query);
	// }

	/**
	 * 定时任务：每天凌晨2:30同步历史NG数据，防止遗漏。
	 */
	// @Scheduled(cron = "0 30 2 * * ?") // 每天2:30执行
	// public void syncHistoricalNgData() {
	// log.info("Starting scheduled daily historical NG serial sync...");
	// AnalyzeQuery query = BuilderUtil.builder(AnalyzeQuery::new)
	// .with(AnalyzeQuery::generateHistoricalTimeRange, 3)
	// .build();
	// executeSyncLogic(query);
	// }

	/**
	 * 同步核心逻辑，分为五个明确的步骤。
	 * @param query 分析范围定义。
	 */
	private void executeSyncLogic(AnalyzeQuery query) {
		try {
			// 步骤1：获取所有AOI设备的潜在NG流水号。
			List<String> potentialNgSerials = aggregatePotentialNgSerials(query);
			log.info("[Step 1/5] Aggregated {} potential NG serials.", potentialNgSerials.size());
			if (potentialNgSerials.isEmpty()) {
				log.info("Sync finished: No potential NG serials found.");
				return;
			}

			// 步骤2：过滤本地数据库中已知的标准样品。
			List<String> afterLocalSampleFilter = filterWithLocalSamples(potentialNgSerials);
			log.info("[Step 2/5] {} serials remain after filtering with local sample data.",
					afterLocalSampleFilter.size());
			if (afterLocalSampleFilter.isEmpty()) {
				log.info("Sync finished: All potential serials were known local samples.");
				return;
			}

			// 步骤3：对剩余流水号，查询MES标准样品，更新本地库，并再次过滤。
			List<String> nonSampleNgSerials = filterWithRemoteSamplesAndSync(afterLocalSampleFilter);
			log.info("[Step 3/5] {} serials remain after filtering with MES sample data.", nonSampleNgSerials.size());
			if (nonSampleNgSerials.isEmpty()) {
				log.info("Sync finished: All remaining serials were identified as samples by MES.");
				return;
			}

			// 步骤4：过滤本地已确认PASS的流水号。
			List<String> serialsToQueryMes = filterLocallyConfirmedPassSerials(nonSampleNgSerials);
			log.info("[Step 4/5] {} serials remain after filtering locally confirmed PASS statuses.",
					serialsToQueryMes.size());
			if (serialsToQueryMes.isEmpty()) {
				log.info("Sync finished: All remaining non-sample NG serials are already confirmed as PASS locally.");
				return;
			}

			// 步骤5：对剩余流水号，查询MES最新状态并更新本地库。
			log.info("[Step 5/5] Querying MES for the latest status of {} serials.", serialsToQueryMes.size());
			updateStatusesFromMes(serialsToQueryMes);

			log.info("Sync process completed successfully.");

		}
		catch (Exception e) {
			log.error("An unexpected error occurred during NG serial sync for query [{}]:", query, e);
		}
	}

	/**
	 * [步骤1] 聚合所有AOI设备的唯一NG流水号。
	 */
	private List<String> aggregatePotentialNgSerials(AnalyzeQuery query) {
		List<Device> aoiDevices = deviceService.getDevicesByCategory(DeviceCategory.AOI);
		if (aoiDevices.isEmpty()) {
			return Collections.emptyList();
		}
		return aoiDevices.parallelStream()
			.flatMap(device -> AoiNgCalculator.findNgSerials(device, query, mongoTemplate).stream())
			.distinct()
			.toList();
	}

	/**
	 * [步骤2] 使用本地数据库过滤掉已知的标准样品。
	 */
	private List<String> filterWithLocalSamples(List<String> serials) {
		List<FromMesSample> localSamples = mesSampleService.findAllByLbIdIn(serials);
		Set<String> localSampleIdSet = localSamples.stream().map(FromMesSample::getLbId).collect(Collectors.toSet());

		if (localSampleIdSet.isEmpty()) {
			return serials;
		}

		return serials.stream().filter(serial -> !localSampleIdSet.contains(serial)).toList();
	}

	/**
	 * [步骤3] 查询MES以识别新样品，同步到本地库，并从列表中过滤。
	 */
	private List<String> filterWithRemoteSamplesAndSync(List<String> serials) {
		// 从MES查询这些流水号中有哪些是样品。
		List<String> newlyFoundSampleIds = mesService.fetchSamples(serials);

		if (CollectionUtils.isEmpty(newlyFoundSampleIds)) {
			// MES没有发现新的样品，直接返回原列表。
			return serials;
		}

		// 将新发现的样品保存到本地数据库，供未来查询。
		log.info("Found {} new sample IDs from MES, saving to local database.", newlyFoundSampleIds.size());
		List<FromMesSample> samplesToSave = newlyFoundSampleIds.stream().map(id -> {
			FromMesSample sample = new FromMesSample();
			sample.setLbId(id);
			return sample;
		}).toList();
		mesSampleService.saveAll(samplesToSave);

		// 从列表中排除新发现的样品。
		Set<String> newlyFoundSampleIdSet = Set.copyOf(newlyFoundSampleIds);
		return serials.stream().filter(serial -> !newlyFoundSampleIdSet.contains(serial)).toList();
	}

	/**
	 * [步骤4] 过滤本地已确认PASS的流水号。
	 */
	private List<String> filterLocallyConfirmedPassSerials(List<String> serials) {
		List<LabelLatestStatus> localPassStatuses = lbwpService.findLatestPassStatusByLbIds(serials);

		Set<String> localPassSerialIdSet = localPassStatuses.stream()
			.map(LabelLatestStatus::getLbId)
			.collect(Collectors.toSet());

		if (localPassSerialIdSet.isEmpty()) {
			return serials;
		}

		return serials.stream().filter(serial -> !localPassSerialIdSet.contains(serial)).toList();
	}

	/**
	 * [步骤5] 批量从MES获取流水号最新状态，并更新本地数据库。
	 */
	private void updateStatusesFromMes(List<String> serials) {
		List<LabelLatestStatus> statusesFromMes = mesService.fetchLatestStatus(serials);

		if (CollectionUtils.isEmpty(statusesFromMes)) {
			log.warn("MES did not return any status updates for {} serials.", serials.size());
			return;
		}

		log.info("Received {} status updates from MES. Performing bulk upsert.", statusesFromMes.size());

		BulkOperations bulkOps = mongoTemplate.bulkOps(BulkOperations.BulkMode.UNORDERED, LabelLatestStatus.class);
		for (LabelLatestStatus status : statusesFromMes) {
			Query upsertQuery = new Query(Criteria.where("lbId").is(status.getLbId()));
			Update update = buildUpdateForStatus(status);
			bulkOps.upsert(upsertQuery, update);
		}
		bulkOps.execute();
	}

	/**
	 * 构建用于批量更新的Update对象。
	 */
	private Update buildUpdateForStatus(LabelLatestStatus status) {
		Update update = new Update();
		update.set("latestIsPass", status.getLatestIsPass());
		update.currentDate("updateTime");
		update.setOnInsert("lbId", status.getLbId());
		update.setOnInsert("createTime", new Date());
		return update;
	}

}