package com.github.cret.web.oee.domain.analyze;

import java.util.List;
import java.util.Map;

/**
 * 不良类型信息
 */
public class DefectTypeResult {

	// 数据头
	List<Map<String, String>> headMaps;

	List<String> dimensions;

	// 数据
	List<Map<String, String>> data;

	public List<Map<String, String>> getHeadMaps() {
		return headMaps;
	}

	public void setHeadMaps(List<Map<String, String>> headMaps) {
		this.headMaps = headMaps;
	}

	public List<Map<String, String>> getData() {
		return data;
	}

	public void setData(List<Map<String, String>> data) {
		this.data = data;
	}

	public List<String> getDimensions() {
		return dimensions;
	}

	public void setDimensions(List<String> dimensions) {
		this.dimensions = dimensions;
	}

}