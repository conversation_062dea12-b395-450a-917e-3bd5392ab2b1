package com.github.cret.web.oee.domain.wxwork;

import java.io.Serial;
import java.io.Serializable;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import org.springframework.util.StringUtils;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.github.cret.web.common.util.JacksonUtil;

/**
 * 消息发送结果对象类. Created by <PERSON><PERSON> on 2017-6-22. 用于封装企业微信消息发送接口的响应结果。
 * 包含错误码、错误信息、无效用户、无效部门、无效标签、消息ID、响应码等字段。
 *
 * <AUTHOR> href="https://github.com/binarywang">Binary Wang</a>
 */
public class MsgRes implements Serializable {

	@Serial
	private static final long serialVersionUID = 916455987193190004L;

	/**
	 * 将当前对象转换为JSON字符串
	 */
	@Override
	public String toString() {
		return JacksonUtil.tryParse(() -> JacksonUtil.getObjectMapper().writeValueAsString(this));
	}

	/**
	 * 从JSON字符串反序列化为MsgRes对象
	 * @param json JSON字符串
	 * @return MsgRes对象
	 */
	public static MsgRes fromJson(String json) {
		return JacksonUtil.tryParse(() -> JacksonUtil.getObjectMapper().readValue(json, MsgRes.class));
	}

	/**
	 * 错误码，0为正常
	 */
	@JsonProperty("errcode")
	private Integer errCode;

	/**
	 * 错误信息
	 */
	@JsonProperty("errmsg")
	private String errMsg;

	/**
	 * 无效的用户ID列表，多个用'|'分隔
	 */
	@JsonProperty("invaliduser")
	private String invalidUser;

	/**
	 * 无效的部门ID列表，多个用'|'分隔
	 */
	@JsonProperty("invalidparty")
	private String invalidParty;

	/**
	 * 无效的标签ID列表，多个用'|'分隔
	 */
	@JsonProperty("invalidtag")
	private String invalidTag;

	/**
	 * 消息ID
	 */
	@JsonProperty("msgid")
	private String msgId;

	/**
	 * 响应码
	 */
	@JsonProperty("response_code")
	private String responseCode;

	/**
	 * 获取无效用户ID列表
	 * @return 无效用户ID集合
	 */
	public List<String> getInvalidUserList() {
		return content2List(this.invalidUser);
	}

	/**
	 * 将'|'分隔的字符串转为List
	 * @param content 字符串内容
	 * @return 字符串集合
	 */
	private List<String> content2List(String content) {
		if (!StringUtils.hasText(content)) {
			return Collections.emptyList();
		}
		return Arrays.asList(content.split("\\|"));
	}

	/**
	 * 获取序列化ID
	 */
	public static long getSerialversionuid() {
		return serialVersionUID;
	}

	public Integer getErrCode() {
		return errCode;
	}

	public void setErrCode(Integer errCode) {
		this.errCode = errCode;
	}

	public String getErrMsg() {
		return errMsg;
	}

	public void setErrMsg(String errMsg) {
		this.errMsg = errMsg;
	}

	public String getInvalidUser() {
		return invalidUser;
	}

	public void setInvalidUser(String invalidUser) {
		this.invalidUser = invalidUser;
	}

	public String getInvalidParty() {
		return invalidParty;
	}

	public void setInvalidParty(String invalidParty) {
		this.invalidParty = invalidParty;
	}

	public String getInvalidTag() {
		return invalidTag;
	}

	public void setInvalidTag(String invalidTag) {
		this.invalidTag = invalidTag;
	}

	public String getMsgId() {
		return msgId;
	}

	public void setMsgId(String msgId) {
		this.msgId = msgId;
	}

	public String getResponseCode() {
		return responseCode;
	}

	public void setResponseCode(String responseCode) {
		this.responseCode = responseCode;
	}

}