package com.github.cret.web.oee.document.feedback;

import org.springframework.data.mongodb.core.mapping.Field;

public class NoticeUser {

	@Field(name = "notice_user_id")
	private String noticeUserId;

	@Field(name = "notice_user_name")
	private String noticeUserName;

	public String getNoticeUserId() {
		return noticeUserId;
	}

	public void setNoticeUserId(String noticeUserId) {
		this.noticeUserId = noticeUserId;
	}

	public String getNoticeUserName() {
		return noticeUserName;
	}

	public void setNoticeUserName(String noticeUserName) {
		this.noticeUserName = noticeUserName;
	}

}
