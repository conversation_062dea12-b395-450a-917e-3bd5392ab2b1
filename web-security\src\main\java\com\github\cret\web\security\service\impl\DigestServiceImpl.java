package com.github.cret.web.security.service.impl;

import com.github.cret.web.security.config.SecurityProperties;
import com.github.cret.web.security.service.DigestService;
import org.apache.commons.codec.digest.DigestUtils;
import org.springframework.stereotype.Service;

@Service
public class DigestServiceImpl implements DigestService {

	private final String PASSWORD_SALT;

	public DigestServiceImpl(SecurityProperties securityProperties) {
		PASSWORD_SALT = securityProperties.getSalt();
	}

	@Override
	public String digestPassword(String password) {
		return DigestUtils.sha512Hex(PASSWORD_SALT + password);
	}

}
