package com.github.cret.web.oee.repository;

import java.util.List;

import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.stereotype.Repository;

import com.github.cret.web.oee.document.mes.FromMesSample;

@Repository
public interface FromMesSampleRepository extends MongoRepository<FromMesSample, String> {

	List<FromMesSample> findAllByLbIdIn(List<String> lbIds);

}
