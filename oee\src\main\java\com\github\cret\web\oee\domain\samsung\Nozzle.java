package com.github.cret.web.oee.domain.samsung;

import org.springframework.data.mongodb.core.mapping.Field;

public record Nozzle(
		// 喷嘴编号
		@Field(name = "nozzlenum") int nozzleNum,
		// 喷嘴型号
		@Field(name = "nozzlechanger") String nozzleChanger,
		// 拾取数量
		@Field(name = "pickcount") int pickCount,
		// 错误数量
		@Field(name = "errorcount") int errorCount,
		// 成功数量
		@Field(name = "successcount") int successCount,
		// 跳过数量
		@Field(name = "skipcount") int skipCount,
		// 漏拾数量
		@Field(name = "misscount") int missCount,
		// 错误拾取数量
		@Field(name = "errorpickcount") int errorPickCount,
		// 位置信息
		@Field(name = "location") String location) {
}
