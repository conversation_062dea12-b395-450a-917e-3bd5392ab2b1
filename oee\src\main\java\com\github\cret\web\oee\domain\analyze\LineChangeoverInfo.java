package com.github.cret.web.oee.domain.analyze;

import java.util.Collections;
import java.util.List;

/**
 * 线体换线次数统计
 */
public class LineChangeoverInfo {

	// 换线次数
	Integer changeoverNum = 0;

	// 换线时间
	Long changeoverTime = 0L;

	// 换线块
	List<LineChangeoverBlock> blocks = Collections.emptyList();

	// 换线时的停机时间
	Integer changeoverStopTime = 0;

	public Integer getChangeoverNum() {
		return changeoverNum;
	}

	public void setChangeoverNum(Integer changeoverNum) {
		this.changeoverNum = changeoverNum;
	}

	public Long getChangeoverTime() {
		return changeoverTime;
	}

	public void setChangeoverTime(Long changeoverTime) {
		this.changeoverTime = changeoverTime;
	}

	public List<LineChangeoverBlock> getBlocks() {
		return blocks;
	}

	public void setBlocks(List<LineChangeoverBlock> blocks) {
		this.blocks = blocks;
	}

	public Integer getChangeoverStopTime() {
		return changeoverStopTime;
	}

	public void setChangeoverStopTime(Integer changeoverStopTime) {
		this.changeoverStopTime = changeoverStopTime;
	}

}
