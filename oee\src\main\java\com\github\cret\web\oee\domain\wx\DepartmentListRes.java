package com.github.cret.web.oee.domain.wx;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

import com.github.cret.web.common.util.JacksonUtil;

/**
 * 部门列表响应
 */
public class DepartmentListRes implements Serializable {

	@Serial
	private static final long serialVersionUID = 1L;

	private Integer errcode;

	private String errmsg;

	private List<Department> department;

	@Override
	public String toString() {
		return JacksonUtil.tryParse(() -> JacksonUtil.getObjectMapper().writeValueAsString(this));
	}

	/**
	 * 从JSON字符串创建对象
	 * @param json JSON字符串
	 * @return 部门列表响应对象
	 */
	public static DepartmentListRes fromJson(String json) {
		return JacksonUtil.tryParse(() -> JacksonUtil.getObjectMapper().readValue(json, DepartmentListRes.class));
	}

	public static long getSerialversionuid() {
		return serialVersionUID;
	}

	public Integer getErrcode() {
		return errcode;
	}

	public void setErrcode(Integer errcode) {
		this.errcode = errcode;
	}

	public String getErrmsg() {
		return errmsg;
	}

	public void setErrmsg(String errmsg) {
		this.errmsg = errmsg;
	}

	public List<Department> getDepartment() {
		return department;
	}

	public void setDepartment(List<Department> department) {
		this.department = department;
	}

}
