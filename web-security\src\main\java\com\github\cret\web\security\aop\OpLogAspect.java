package com.github.cret.web.security.aop;

import com.github.cret.web.common.annotation.OpLog;
import com.github.cret.web.common.util.JsonUtil;
import com.github.cret.web.security.domain.AuthUser;
import com.github.cret.web.security.util.SecurityUtil;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.reflect.MethodSignature;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;

@Aspect
@Component
public class OpLogAspect {

	private final Logger logger = LoggerFactory.getLogger("operation-log");

	@Before("@annotation(com.github.cret.web.common.annotation.OpLog)")
	public void logMethod(final JoinPoint joinPoint) {
		try {
			AuthUser authUser = SecurityUtil.getCurrentUser();
			Method method = ((MethodSignature) joinPoint.getSignature()).getMethod();
			OpLog opLog = method.getAnnotation(OpLog.class);
			Object[] args = joinPoint.getArgs();
			String params = JsonUtil.stringify(args);
			assert authUser != null;
			logger.info("{\"user\":\"{}\", op: \"{}\", method: \"{}\", args: {} }", authUser.name(), opLog.name(),
					joinPoint.getSignature().toString(), params);
		}
		catch (Exception e) {
			logger.error("log failed", e);
		}
	}

}
