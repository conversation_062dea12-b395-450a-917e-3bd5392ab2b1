package com.github.cret.web.security.service;

import com.github.cret.web.common.domain.PageList;
import com.github.cret.web.common.domain.PageableParam;
import com.github.cret.web.security.document.User;
import com.github.cret.web.security.domain.AuthUser;
import com.github.cret.web.security.domain.UserCreate;
import com.github.cret.web.security.domain.UserQuery;
import com.github.cret.web.security.domain.UserUpdate;
import com.github.cret.web.security.domain.UserView;

public interface UserService {

	/**
	 * get AuthUser by name TODO throw exception
	 * @param username
	 * @return
	 */
	AuthUser findAuthUserByUsername(String username);

	UserView get(String id);

	User create(UserCreate userCreate);

	void lock(String id);

	// 修改密码
	Boolean changePassword(String oldPassword, String newPassword);

	void update(String id, UserUpdate userUpdate);

	void delete(String id);

	PageList<UserView> page(PageableParam<UserQuery> queryParam);

}
