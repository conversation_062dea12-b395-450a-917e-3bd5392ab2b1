package com.github.cret.web.security.service.impl;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.github.cret.web.common.enumerate.SysErrEnum;
import com.github.cret.web.common.exception.SystemException;
import com.github.cret.web.security.config.CasdoorService;
import com.github.cret.web.security.domain.AuthUser;
import com.github.cret.web.security.domain.LoginRes;
import com.github.cret.web.security.service.AuthService;
import com.github.cret.web.security.service.DigestService;
import com.github.cret.web.security.service.TokenService;
import com.github.cret.web.security.service.UserService;
import org.casbin.casdoor.entity.User;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;

@Service
public class AuthServiceImpl implements AuthService {

	private final UserService userService;

	private final DigestService digestService;

	private final TokenService tokenService;

	private final CasdoorService casdoorService;

	public AuthServiceImpl(UserService userService, DigestService digestService, TokenService tokenService,
			CasdoorService casdoorService) {
		this.userService = userService;
		this.digestService = digestService;
		this.tokenService = tokenService;
		this.casdoorService = casdoorService;
	}

	@Override
	public LoginRes loginByPassword(String username, String password) {
		try {
			AuthUser authUser = userService.findAuthUserByUsername(username);
			String digestPassword = digestService.digestPassword(password);
			if (!digestPassword.equals(authUser.password())) {
				throw SysErrEnum.LOGIN_FAIL.exception("密码错误");
			}
			String accessToken = tokenService.issueAccessToken(authUser);
			String refreshToken = tokenService.issueRefreshToken(authUser);
			return new LoginRes(authUser, accessToken, refreshToken);
		}
		catch (SystemException | JsonProcessingException ex) {
			throw SysErrEnum.LOGIN_FAIL.exception(ex.getMessage(), ex);
		}
	}

	@Override
	public LoginRes loginByUsername(String username) {
		try {
			AuthUser authUser = userService.findAuthUserByUsername(username);
			String accessToken = tokenService.issueAccessToken(authUser);
			String refreshToken = tokenService.issueRefreshToken(authUser);
			return new LoginRes(authUser, accessToken, refreshToken);
		}
		catch (SystemException | JsonProcessingException ex) {
			throw SysErrEnum.LOGIN_FAIL.exception(ex.getMessage(), ex);
		}
	}

	@Override
	public LoginRes loginByCasdoor(String code, String state) {
		User user = casdoorService.auth(code, state);
		return loginByUsername(user.name);
	}

	@Override
	public AuthUser getUserInfo() {
		return (AuthUser) SecurityContextHolder.getContext().getAuthentication().getPrincipal();
	}

	@Override
	public String refreshToken(String refreshToken) {
		return tokenService.refresh(refreshToken);
	}

}
