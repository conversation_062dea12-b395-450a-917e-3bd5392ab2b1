package com.github.cret.web.oee.service.impl;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Optional;

import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import com.github.cret.web.common.domain.PageList;
import com.github.cret.web.common.enumerate.SysErrEnum;
import com.github.cret.web.common.exception.SystemException;
import com.github.cret.web.oee.document.analyze.Abnormal;
import com.github.cret.web.oee.document.feedback.AnomaliesClassification;
import com.github.cret.web.oee.document.feedback.FeedbackTriggerRecord;
import com.github.cret.web.oee.document.feedback.FeedbackTriggerSend;
import com.github.cret.web.oee.document.feedback.FeedbackTriggerSolution;
import com.github.cret.web.oee.document.feedback.NoticeUser;
import com.github.cret.web.oee.document.feedback.SendStatus;
import com.github.cret.web.oee.domain.query.FeedbackTriggerRecordQuery;
import com.github.cret.web.oee.repository.AnomaliesClassificationRepository;
import com.github.cret.web.oee.repository.FeedbackTriggerRecordRepository;
import com.github.cret.web.oee.repository.FeedbackTriggerSolutionRepository;
import com.github.cret.web.oee.service.AbnormalService;
import com.github.cret.web.oee.service.FeedbackTriggerRecordService;
import com.github.cret.web.oee.service.FeedbackTriggerSendService;
import com.github.cret.web.security.domain.AuthUser;
import com.github.cret.web.security.util.SecurityUtil;

@Service
public class FeedbackTriggerRecordServiceImpl implements FeedbackTriggerRecordService {

	private final FeedbackTriggerRecordRepository repository;

	private final AnomaliesClassificationRepository anomaliesClassificationRepository;

	private final FeedbackTriggerSendService feedbackTriggerSendService;

	private final AbnormalService abnormalService;

	private final FeedbackTriggerSolutionRepository feedbackTriggerSolutionRepository;

	private final MongoTemplate mongoTemplate;

	public FeedbackTriggerRecordServiceImpl(FeedbackTriggerRecordRepository repository,
			AnomaliesClassificationRepository anomaliesClassificationRepository,
			FeedbackTriggerSendService feedbackTriggerSendService, AbnormalService abnormalService,
			FeedbackTriggerSolutionRepository feedbackTriggerSolutionRepository, MongoTemplate mongoTemplate) {
		this.repository = repository;
		this.anomaliesClassificationRepository = anomaliesClassificationRepository;
		this.feedbackTriggerSendService = feedbackTriggerSendService;
		this.abnormalService = abnormalService;
		this.feedbackTriggerSolutionRepository = feedbackTriggerSolutionRepository;
		this.mongoTemplate = mongoTemplate;
	}

	@Override
	@Transactional
	public FeedbackTriggerRecord save(FeedbackTriggerRecord record) {
		// 设置触发时间，如果未设置则使用当前时间
		record.setTriggerTime(Optional.ofNullable(record.getTriggerTime()).orElse(new Date()));

		// 设置触发人信息
		AuthUser authUser = SecurityUtil.getCurrentUser();
		record.setTriggerUserId(Optional.ofNullable(record.getTriggerUserId()).orElse(authUser.id()));
		record.setTriggerUserName(Optional.ofNullable(record.getTriggerUserName()).orElse(authUser.name()));

		// 初始设置异常未关闭
		record.setTriggerClose(false);

		// 先保存触发记录以获取ID
		FeedbackTriggerRecord savedRecord = repository.save(record);

		// 创建发送记录，根据异常编码和线体编码查找异常分类配置，根据异常分类配置生成发送表
		anomaliesClassificationRepository
			.findByLineCodeAndAnomaliesCode(savedRecord.getLineCode(), savedRecord.getAnomaliesCode())
			.map(AnomaliesClassification::getResponseConfig)
			.filter(configs -> !configs.isEmpty())
			.ifPresent(responseConfigs -> responseConfigs
				.forEach(config -> feedbackTriggerSendService.createFeedbackTriggerSend(savedRecord, config)));

		return savedRecord;
	}

	@Override
	public void delete(String id) {
		repository.deleteById(id);
	}

	@Override
	public FeedbackTriggerRecord findById(String id) {
		return repository.findById(id).orElseThrow(() -> new SystemException(SysErrEnum.NOT_FOUND));
	}

	@Override
	public PageList<FeedbackTriggerRecord> search(FeedbackTriggerRecordQuery query) {
		Query mongoQuery = new Query();
		List<Criteria> criteria = new ArrayList<>();

		if (StringUtils.hasText(query.getAnomaliesCode())) {
			criteria.add(Criteria.where("anomaliesCode").is(query.getAnomaliesCode()));
		}

		if (StringUtils.hasText(query.getLineCode())) {
			criteria.add(Criteria.where("lineCode").is(query.getLineCode()));
		}

		if (StringUtils.hasText(query.getTriggerId())) {
			criteria.add(Criteria.where("triggerId").is(query.getTriggerId()));
		}

		if (query.getStartTime() != null) {
			criteria.add(Criteria.where("triggerTime").gte(query.getStartTime()));
		}

		if (query.getEndTime() != null) {
			criteria.add(Criteria.where("triggerTime").lte(query.getEndTime()));
		}

		if (!criteria.isEmpty()) {
			mongoQuery.addCriteria(new Criteria().andOperator(criteria.toArray(new Criteria[0])));
		}

		long total = mongoTemplate.count(mongoQuery, FeedbackTriggerRecord.class);

		mongoQuery.with(query.getPageRequest());
		List<FeedbackTriggerRecord> list = mongoTemplate.find(mongoQuery, FeedbackTriggerRecord.class);

		PageList<FeedbackTriggerRecord> result = new PageList<>();
		result.setList(list);
		result.setTotal(total);
		result.setHasNext((long) query.getPageRequest().getOffset() + query.getPageRequest().getPageSize() < total);

		return result;
	}

	@Override
	public FeedbackTriggerRecord update(String id, FeedbackTriggerRecord record) {
		FeedbackTriggerRecord existing = findById(id);
		record.setId(existing.getId());
		return repository.save(record);
	}

	@Override
	public boolean hasOpenExceptions(String lineCode) {
		Query query = new Query();
		query.addCriteria(Criteria.where("lineCode").is(lineCode).and("triggerClose").is(false));
		return mongoTemplate.exists(query, FeedbackTriggerRecord.class);
	}

	@Override
	public FeedbackTriggerRecord findLatestOpenException(String lineCode) {
		Query query = new Query();
		query.addCriteria(Criteria.where("lineCode").is(lineCode).and("triggerClose").is(false));
		query.with(Sort.by(Sort.Direction.DESC, "triggerTime"));
		query.limit(1);
		return mongoTemplate.findOne(query, FeedbackTriggerRecord.class);
	}

	@Override
	@Transactional
	public FeedbackTriggerRecord closeException(String id) {
		return repository.findById(id).map(record -> {
			// 关闭异常记录
			record.setTriggerClose(true);
			record.setTriggerCloseTime(new Date());
			FeedbackTriggerRecord savedRecord = repository.save(record);

			// 将异常信息和解决方案保存到 t_abnormal 表
			saveToAbnormalTable(record);

			// 停止对应的未发送记录
			stopUnsentTriggerSendRecords(id);

			return savedRecord;
		}).orElseThrow(() -> new SystemException(SysErrEnum.NOT_FOUND));
	}

	@Override
	@Transactional
	public int batchCloseException(List<String> ids) {
		if (ids == null || ids.isEmpty()) {
			return 0;
		}

		Date closeTime = new Date();
		int successCount = 0;

		for (String id : ids) {
			try {
				boolean updated = repository.findById(id).map(record -> {
					record.setTriggerClose(true);
					record.setTriggerCloseTime(closeTime);
					repository.save(record);

					// 将异常信息和解决方案保存到 t_abnormal 表
					saveToAbnormalTable(record);

					// 停止对应的未发送记录
					stopUnsentTriggerSendRecords(id);

					return true;
				}).orElse(false);

				if (updated) {
					successCount++;
				}
			}
			catch (Exception e) {
				// 记录错误但继续处理其他记录
				// 可以考虑添加日志记录
			}
		}

		return successCount;
	}

	@Override
	public List<NoticeUser> getNoticeUsers(String id) {
		FeedbackTriggerRecord record = findById(id);
		List<NoticeUser> noticeUsers = record.getNoticeUsers();
		return noticeUsers != null ? noticeUsers : new ArrayList<>();
	}

	/**
	 * 将异常信息和解决方案保存到 t_abnormal 表
	 * @param record 触发记录
	 */
	private void saveToAbnormalTable(FeedbackTriggerRecord record) {
		Abnormal abnormal = new Abnormal();

		// 设置基本信息
		abnormal.setLineCode(record.getLineCode());
		abnormal.setClassification(record.getAnomaliesCode());
		abnormal.setCause(record.getAnomaliesDetail());
		abnormal.setStartTime(record.getAnomaliesStartTime());
		abnormal.setEndTime(record.getTriggerCloseTime());

		// 设置填写人为触发人，填写时间为触发时间
		abnormal.setWriter(record.getTriggerUserName());
		abnormal.setWriteTime(record.getTriggerTime());

		// 设置确认人为触发人，确认时间为异常关闭时间
		abnormal.setConfirm(record.getTriggerUserName());
		abnormal.setConfirmTime(record.getTriggerCloseTime());

		// 从 t_feedback_trigger_solution 表获取解决方案内容
		String solutionText = getSolutionFromTable(record.getId());
		abnormal.setSolution(solutionText);

		// 计算持续时间
		if (record.getAnomaliesStartTime() != null && record.getTriggerCloseTime() != null) {
			long duration = record.getTriggerCloseTime().getTime() - record.getAnomaliesStartTime().getTime();
			abnormal.setDuration(duration);
			abnormal.setAbnormalTime(duration / (1000.0 * 60 * 60)); // 转换为小时
		}

		// 责任人和责任部门留空

		// 保存到异常表
		abnormalService.save(abnormal);
	}

	/**
	 * 停止触发记录对应的未发送记录
	 * @param triggerRecordId 触发记录ID
	 */
	private void stopUnsentTriggerSendRecords(String triggerRecordId) {
		try {
			// 查找该触发记录对应的所有未发送记录
			List<FeedbackTriggerSend> unsentRecords = feedbackTriggerSendService
				.findByTriggerRecordIdAndSendStatus(triggerRecordId, SendStatus.UNSENT.getCode());

			if (unsentRecords != null && !unsentRecords.isEmpty()) {
				// 更新每条未发送记录的状态为停止发送
				for (FeedbackTriggerSend record : unsentRecords) {
					((FeedbackTriggerSendServiceImpl) feedbackTriggerSendService).updateSendStatus(record.getId(),
							SendStatus.STOPPED, "异常已关闭，停止发送");
				}
			}
		}
		catch (Exception e) {
			// 记录错误但不影响主流程
			// 可以考虑添加日志记录
		}
	}

	/**
	 * 从 t_feedback_trigger_solution 表获取解决方案内容
	 * @param triggerRecordId 触发记录ID
	 * @return 解决方案文本
	 */
	private String getSolutionFromTable(String triggerRecordId) {
		try {
			// 根据触发记录ID查找解决方案列表
			List<FeedbackTriggerSolution> solutions = feedbackTriggerSolutionRepository
				.findByTriggerRecordId(triggerRecordId);

			if (solutions == null || solutions.isEmpty()) {
				return "";
			}

			// 根据您的说明，一个triggerRecordId只有唯一解决对象
			// 但为了保险起见，我们仍然处理可能的多个解决方案情况
			StringBuilder solutionBuilder = new StringBuilder();
			for (int i = 0; i < solutions.size(); i++) {
				FeedbackTriggerSolution solution = solutions.get(i);
				if (solution != null && solution.getSolution() != null && !solution.getSolution().trim().isEmpty()) {
					if (solutionBuilder.length() > 0) {
						solutionBuilder.append("; ");
					}
					// 如果只有一个解决方案，不需要编号
					if (solutions.size() == 1) {
						solutionBuilder.append(solution.getSolution().trim());
					}
					else {
						solutionBuilder.append("解决方案").append(i + 1).append(": ").append(solution.getSolution().trim());
					}
				}
			}

			return solutionBuilder.toString();
		}
		catch (Exception e) {
			// 如果获取解决方案失败，返回空字符串，不影响异常记录的保存
			return "";
		}
	}

}
