package com.github.cret.web.oee.controller;

import java.util.Collections;
import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.github.cret.web.oee.domain.analyze.AnalyzeQuery;
import com.github.cret.web.oee.task.NgSerialSyncTask;

import cn.hutool.core.date.DateUtil;

@RestController
@RequestMapping("/api/tasks")
public class TaskController {

	private static final Logger log = LoggerFactory.getLogger(TaskController.class);

	private final NgSerialSyncTask ngSerialSyncTask;

	public TaskController(NgSerialSyncTask ngSerialSyncTask) {
		this.ngSerialSyncTask = ngSerialSyncTask;
	}

	@PostMapping("/sync-ng-serials")
	public ResponseEntity<Map<String, String>> triggerNgSerialSync(@RequestParam(required = false) String startTime,
			@RequestParam(required = false) String endTime) {

		log.info("Received request to trigger NG serial synchronization task with startTime: {} and endTime: {}.",
				startTime, endTime);

		try {
			AnalyzeQuery query = new AnalyzeQuery();
			if (startTime != null) {
				query.setStartTime(DateUtil.parse(startTime));
			}
			if (endTime != null) {
				query.setEndTime(DateUtil.parse(endTime));
			}
			// 调用同步任务
			ngSerialSyncTask.syncNgSerials(query);

			log.info("NG serial sync task has been started successfully.");

			Map<String, String> response = Collections.singletonMap("message",
					"NG serial sync task started successfully.");
			return ResponseEntity.ok(response);

		}
		catch (Exception e) {
			log.error("Failed to execute NG serial sync task due to an unexpected error.", e);

			Map<String, String> errorResponse = Collections.singletonMap("error",
					"Failed to start task. Check server logs for details.");
			return ResponseEntity.internalServerError().body(errorResponse);
		}
	}

}
