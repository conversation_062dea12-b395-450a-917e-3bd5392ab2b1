package com.github.cret.web.oee.domain.abnormal;

import java.util.Date;

public class AbnormalSearch {

	private String id;

	// 产线编码
	private String lineCode;

	// 异常分类
	private String classification;

	// 异常原因
	private String cause;

	// 开始时间
	private Date startTime;

	// 结束时间
	private Date endTime;

	// 责任人
	private String responsible;

	// 责任部门
	private String department;

	// 填写人
	private String writer;

	// 填写开始时间
	private Date writeStartTime;

	// 填写结束时间
	private Date writeEndTime;

	// 确认人
	private String confirm;

	// 确认开始时间
	private Date confirmStartTime;

	// 确认结束时间
	private Date confirmEndTime;

	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	}

	public String getLineCode() {
		return lineCode;
	}

	public void setLineCode(String lineCode) {
		this.lineCode = lineCode;
	}

	public String getClassification() {
		return classification;
	}

	public void setClassification(String classification) {
		this.classification = classification;
	}

	public String getCause() {
		return cause;
	}

	public void setCause(String cause) {
		this.cause = cause;
	}

	public Date getStartTime() {
		return startTime;
	}

	public void setStartTime(Date startTime) {
		this.startTime = startTime;
	}

	public Date getEndTime() {
		return endTime;
	}

	public void setEndTime(Date endTime) {
		this.endTime = endTime;
	}

	public String getResponsible() {
		return responsible;
	}

	public void setResponsible(String responsible) {
		this.responsible = responsible;
	}

	public String getDepartment() {
		return department;
	}

	public void setDepartment(String department) {
		this.department = department;
	}

	public String getWriter() {
		return writer;
	}

	public void setWriter(String writer) {
		this.writer = writer;
	}

	public Date getWriteStartTime() {
		return writeStartTime;
	}

	public void setWriteStartTime(Date writeStartTime) {
		this.writeStartTime = writeStartTime;
	}

	public Date getWriteEndTime() {
		return writeEndTime;
	}

	public void setWriteEndTime(Date writeEndTime) {
		this.writeEndTime = writeEndTime;
	}

	public String getConfirm() {
		return confirm;
	}

	public void setConfirm(String confirm) {
		this.confirm = confirm;
	}

	public Date getConfirmStartTime() {
		return confirmStartTime;
	}

	public void setConfirmStartTime(Date confirmStartTime) {
		this.confirmStartTime = confirmStartTime;
	}

	public Date getConfirmEndTime() {
		return confirmEndTime;
	}

	public void setConfirmEndTime(Date confirmEndTime) {
		this.confirmEndTime = confirmEndTime;
	}

}
