package com.github.cret.web.oee.domain.deviceConfig;

import org.springframework.data.mongodb.core.mapping.Field;

public class Output {

	@Field(name = "collection")
	private String collection;

	@Field(name = "type")
	private String type;

	public String getCollection() {
		return collection;
	}

	public void setCollection(String collection) {
		this.collection = collection;
	}

	public String getType() {
		return type;
	}

	public void setType(String type) {
		this.type = type;
	}

}
