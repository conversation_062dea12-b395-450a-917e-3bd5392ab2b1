package com.github.cret.web.oee.calculator;

import com.github.cret.web.oee.document.Device;
import com.github.cret.web.oee.domain.analyze.AnalyzeQuery;
import com.github.cret.web.oee.domain.analyze.HourlyOutput;
import com.github.cret.web.oee.domain.analyze.HourlyRunTimeGroup;
import com.github.cret.web.oee.utils.DocumentUtil;
import com.github.cret.web.oee.utils.OeeUtil;
import org.bson.Document;
import org.springframework.data.mongodb.core.MongoTemplate;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

public class HourlyOutputCalculator {

	/**
	 * 计算松下贴片机的每小时产出
	 * @param device 设备
	 * @param query 查询条件
	 * @param mongoTemplate 数据库操作对象
	 * @return 每小时产出
	 */
	public static List<HourlyOutput> getNpmSmtHourlyOutput(Device device, AnalyzeQuery query,
			MongoTemplate mongoTemplate) {
		// 获取生产日志集合
		String collectionName = OeeUtil.getProductionLogCollection(device.getCode());

		// 构建聚合管道
		List<Document> pipeline = new ArrayList<>();

		// 1. 匹配时间范围和产品型号（如果有）
		Document matchDoc = new Document("time",
				new Document("$gte", query.getStartTime()).append("$lte", query.getEndTime()));

		// 如果指定了产品型号，添加产品型号过滤条件
		if (query.getProductModel() != null && !query.getProductModel().isEmpty()) {
			matchDoc.append("productmodel", query.getProductModel());
		}

		pipeline.add(new Document("$match", matchDoc));
		// 2. 按时间排序
		pipeline.add(new Document("$sort", new Document("time", 1)));

		// 3. 添加前一个值字段
		pipeline.add(new Document("$setWindowFields", new Document()
			.append("sortBy", new Document("time", 1).append("totaltime", -1))
			.append("output", new Document().append("previousValue",
					new Document("$shift", new Document().append("output", "$raw.count.module").append("by", -1))))));

		// 4. 添加计算字段
		pipeline
			.add(new Document("$addFields",
					new Document("result",
							new Document("$cond",
									new Document()
										.append("if",
												new Document("$gte",
														Arrays.asList("$raw.count.module", "$previousValue")))
										.append("then",
												new Document("$subtract",
														Arrays.asList("$raw.count.module", "$previousValue")))
										.append("else", "$raw.count.module")))));

		// 5. 过滤掉第一条记录
		pipeline.add(new Document("$match", new Document("result", new Document("$ne", null))));

		// 6. 添加小时分组字段（带时区）
		pipeline.add(new Document("$addFields",
				new Document("hour",
						new Document("$dateToString",
								new Document().append("format", "%Y-%m-%d %H:00:00")
									.append("date", "$time")
									.append("timezone", "Asia/Shanghai")))));

		// 7. 汇总计算结果
		pipeline.add(new Document("$group",
				new Document().append("_id", "$hour").append("total", new Document("$sum", "$result"))));

		// 执行聚合查询
		List<Document> results = mongoTemplate.getCollection(collectionName)
			.aggregate(pipeline)
			.into(new ArrayList<>());

		// 转换结果为ActualRunTime对象列表
		List<HourlyOutput> hourlyOutputs = new ArrayList<>();

		for (Document doc : results) {
			HourlyOutput hourlyOutput = new HourlyOutput();
			hourlyOutput.setTime(doc.getString("_id"));
			hourlyOutput.setCount(doc.getInteger("total"));
			hourlyOutputs.add(hourlyOutput);
		}

		return hourlyOutputs;
	}

	/**
	 * 获取松下贴片机的每小时产品运行时间(使用totaltime作为运行时间)，totalTime中不包含停机时间
	 * @param query 查询条件
	 * @param mongoTemplate MongoDB操作模板
	 * @return 运行时间列表
	 */
	public static List<HourlyRunTimeGroup> getNpmSmtHourlyRunTime(Device device, AnalyzeQuery query,
			MongoTemplate mongoTemplate) {

		// 获取生产日志集合
		String collectionName = OeeUtil.getProductionLogCollection(device.getCode());

		// 构建聚合管道
		List<Document> pipeline = new ArrayList<>();

		// 1. 匹配时间范围和产品型号
		Document matchDoc = new Document("time",
				new Document("$gte", query.getStartTime()).append("$lte", query.getEndTime()));
		if (query.getProductModel() != null && !query.getProductModel().isEmpty()) {
			matchDoc.append("productmodel", query.getProductModel());
		}
		pipeline.add(new Document("$match", matchDoc));

		// 2. 按时间排序
		pipeline.add(new Document("$sort", new Document("time", 1)));

		// 3. 添加前一个值字段
		pipeline.add(new Document("$setWindowFields",
				new Document().append("sortBy", new Document("time", 1).append("totaltime", -1))
					.append("output", new Document().append("previousValue",
							new Document("$shift", new Document().append("output", "$totaltime").append("by", -1))))));

		// 4. 计算运行时间差值
		pipeline.add(new Document("$addFields",
				new Document("result", new Document("$cond",
						new Document().append("if", new Document("$gte", Arrays.asList("$totaltime", "$previousValue")))
							.append("then", new Document("$subtract", Arrays.asList("$totaltime", "$previousValue")))
							.append("else", "$totaltime")))));

		// 5. 过滤掉空值
		pipeline.add(new Document("$match", new Document("result", new Document("$ne", null))));

		// 6. 添加小时分组字段（带时区）
		pipeline.add(new Document("$addFields",
				new Document("hour",
						new Document("$dateToString",
								new Document().append("format", "%Y-%m-%d %H:00:00")
									.append("date", "$time")
									.append("timezone", "Asia/Shanghai")))));

		// 7. 按产品型号和小时分组，同时保留最早的时间信息
		pipeline.add(new Document("$group",
				new Document()
					.append("_id", new Document().append("productmodel", "$productmodel").append("hour", "$hour"))
					.append("total", new Document("$sum", "$result"))
					.append("firstTime", new Document("$min", "$time")) // 添加最早时间
		));

		// 8. 按时间和产品型号排序
		pipeline.add(new Document("$sort", new Document("_id.hour", 1).append("firstTime", 1) // 在同一小时内按照最早时间排序
			.append("_id.productmodel", 1)));

		// 执行聚合查询
		List<Document> results = mongoTemplate.getCollection(collectionName)
			.aggregate(pipeline)
			.into(new ArrayList<>());

		// 转换结果
		List<HourlyRunTimeGroup> hourlyRunTimes = new ArrayList<>();
		for (Document doc : results) {
			Document idDoc = (Document) doc.get("_id");
			HourlyRunTimeGroup hourlyRunTime = new HourlyRunTimeGroup();
			hourlyRunTime.setProductModel(idDoc.getString("productmodel"));
			hourlyRunTime.setHour(idDoc.getString("hour"));
			hourlyRunTime.setRunTime(doc.getDouble("total"));
			hourlyRunTime.setFirstTime(doc.getDate("firstTime"));
			hourlyRunTimes.add(hourlyRunTime);
		}

		return hourlyRunTimes;
	}

	/**
	 * 获取三星的每小时实际产出
	 * @param device
	 * @param query
	 * @param mongoTemplate
	 * @return
	 */
	public static List<HourlyOutput> getSamsungSmtHourlyOutput(Device device, AnalyzeQuery query,
			MongoTemplate mongoTemplate) {
		// 获取生产日志集合名称
		String collectionName = OeeUtil.getProductionLogCollection(device.getCode());

		// 构建聚合管道
		List<Document> pipeline = new ArrayList<>();

		// Step 1: 匹配时间范围和产品型号
		Document matchDoc = new Document("time",
				new Document("$gte", query.getStartTime()).append("$lte", query.getEndTime()));
		matchDoc.append("normalflag", true);
		if (query.getProductModel() != null && !query.getProductModel().isEmpty()) {
			matchDoc.append("productmodel", query.getProductModel());
		}
		pipeline.add(new Document("$match", matchDoc));

		// Step 2: 按时间排序
		pipeline.add(new Document("$sort", new Document("time", 1)));

		// Step 3: 添加前一个值字段（通过窗口函数）
		pipeline.add(new Document("$setWindowFields", new Document()
			.append("sortBy", new Document("time", 1).append("samsungindex.pcbcount", -1))
			.append("output", new Document("previousValue",
					new Document("$shift", new Document("output", "$samsungindex.pcbcount").append("by", -1))))));

		// Step 4: 计算 result 字段
		pipeline.add(new Document("$addFields",
				new Document("result",
						new Document("$cond",
								new Document()
									.append("if",
											new Document("$gte",
													Arrays.asList("$samsungindex.pcbcount", "$previousValue")))
									.append("then",
											new Document("$subtract",
													Arrays.asList("$samsungindex.pcbcount", "$previousValue")))
									.append("else", "$samsungindex.pcbcount")))));

		// Step 5: 过滤掉第一条记录（result 为空的情况）
		pipeline.add(new Document("$match", new Document("result", new Document("$ne", null))));

		// Step 6: 添加小时分组字段（带时区）
		pipeline.add(new Document("$addFields",
				new Document("hour",
						new Document("$dateToString",
								new Document("format", "%Y-%m-%d %H:00:00").append("date", "$time")
									.append("timezone", "Asia/Shanghai")))));

		// Step 7: 按小时分组并累加 result
		pipeline
			.add(new Document("$group", new Document("_id", "$hour").append("total", new Document("$sum", "$result"))));

		// Step 8: 按小时排序
		pipeline.add(new Document("$sort", new Document("_id", 1)));

		// 执行聚合查询
		List<Document> results = mongoTemplate.getCollection(collectionName)
			.aggregate(pipeline)
			.into(new ArrayList<>());

		// 转换结果为ActualRunTime对象列表
		List<HourlyOutput> hourlyOutputs = new ArrayList<>();

		for (Document doc : results) {
			HourlyOutput hourlyOutput = new HourlyOutput();
			hourlyOutput.setTime(doc.getString("_id"));
			hourlyOutput.setCount(doc.getInteger("total"));
			hourlyOutputs.add(hourlyOutput);
		}

		return hourlyOutputs;
	}

	/**
	 * 获取三星的运行时间
	 * @param device
	 * @param query
	 * @param mongoTemplate
	 * @return
	 */
	public static List<HourlyRunTimeGroup> getSamsungSmtHourlyRunTime(Device device, AnalyzeQuery query,
			MongoTemplate mongoTemplate) {
		// 获取生产日志集合名称
		String collectionName = OeeUtil.getProductionLogCollection(device.getCode());

		// 构建聚合管道
		List<Document> pipeline = new ArrayList<>();

		// 1. 匹配时间范围和产品型号
		Document matchDoc = new Document("time",
				new Document("$gte", query.getStartTime()).append("$lte", query.getEndTime()));
		matchDoc.append("normalflag", true);
		if (query.getProductModel() != null && !query.getProductModel().isEmpty()) {
			matchDoc.append("productmodel", query.getProductModel());
		}
		pipeline.add(new Document("$match", matchDoc));

		// 2. 按时间排序
		pipeline.add(new Document("$sort", new Document("time", 1)));

		// 3. 计算 totaltime（runtime + stoptime + idletime）
		pipeline.add(new Document("$addFields", new Document("totaltime", new Document("$add",
				Arrays.asList("$samsungindex.runtime", "$samsungindex.stoptime", "$samsungindex.idletime")))));

		// 4. 添加前一个值字段（通过窗口函数）
		pipeline.add(new Document("$setWindowFields",
				new Document().append("sortBy", new Document("time", 1).append("totaltime", -1))
					.append("output", new Document().append("previousValue",
							new Document("$shift", new Document().append("output", "$totaltime").append("by", -1))))));

		// 5. 计算运行时间差值
		pipeline.add(new Document("$addFields",
				new Document("result", new Document("$cond",
						new Document().append("if", new Document("$gte", Arrays.asList("$totaltime", "$previousValue")))
							.append("then", new Document("$subtract", Arrays.asList("$totaltime", "$previousValue")))
							.append("else", "$totaltime")))));

		// 6. 过滤掉空值
		pipeline.add(new Document("$match", new Document("result", new Document("$ne", null))));

		// 7. 添加小时分组字段（带时区）
		pipeline.add(new Document("$addFields",
				new Document("hour",
						new Document("$dateToString",
								new Document().append("format", "%Y-%m-%d %H:00:00")
									.append("date", "$time")
									.append("timezone", "Asia/Shanghai")))));

		// 8. 按产品型号和小时分组，同时保留最早的时间信息
		pipeline.add(new Document("$group",
				new Document()
					.append("_id", new Document().append("productmodel", "$productmodel").append("hour", "$hour"))
					.append("total", new Document("$sum", "$result"))
					.append("firstTime", new Document("$min", "$time")) // 添加最早时间
		));

		// 9. 按时间和产品型号排序
		pipeline.add(new Document("$sort", new Document("_id.hour", 1).append("firstTime", 1) // 在同一小时内按照最早时间排序
			.append("_id.productmodel", 1)));

		// 执行聚合查询
		List<Document> results = mongoTemplate.getCollection(collectionName)
			.aggregate(pipeline)
			.into(new ArrayList<>());

		// 转换结果
		List<HourlyRunTimeGroup> hourlyRunTimes = new ArrayList<>();
		for (Document doc : results) {
			Document idDoc = (Document) doc.get("_id");
			HourlyRunTimeGroup hourlyRunTime = new HourlyRunTimeGroup();
			hourlyRunTime.setProductModel(idDoc.getString("productmodel"));
			hourlyRunTime.setHour(idDoc.getString("hour"));
			Object total = doc.get("total");
			double totalRunTime = DocumentUtil.getDouble(total);
			hourlyRunTime.setRunTime(totalRunTime);
			hourlyRunTime.setFirstTime(doc.getDate("firstTime"));
			hourlyRunTimes.add(hourlyRunTime);
		}

		return hourlyRunTimes;
	}

	/**
	 * 获取yamaha的每小时产出
	 * @param device
	 * @param query
	 * @param mongoTemplate
	 * @return
	 */
	public static List<HourlyOutput> getYamahaSmtHourlyOutput(Device device, AnalyzeQuery query,
			MongoTemplate mongoTemplate) {
		// 获取生产日志集合
		String collectionName = OeeUtil.getProductionLogCollection(device.getCode());

		// 构建聚合管道
		List<Document> pipeline = new ArrayList<>();

		// 1. 匹配时间范围和产品型号（如果有）
		Document matchDoc = new Document("time",
				new Document("$gte", query.getStartTime()).append("$lte", query.getEndTime()));
		if (query.getProductModel() != null && !query.getProductModel().isEmpty()) {
			matchDoc.append("productmodel", query.getProductModel());
		}
		pipeline.add(new Document("$match", matchDoc));

		// 2. 按时间排序
		pipeline.add(new Document("$sort", new Document("time", 1)));

		// 3. 计算所属小时（带时区）
		pipeline.add(new Document("$addFields", new Document("hour", new Document("$dateTrunc",
				new Document("date", "$time").append("unit", "hour").append("timezone", "Asia/Shanghai")))));

		// 4. 确定相邻产品变化
		pipeline.add(new Document("$setWindowFields",
				new Document("partitionBy", null).append("sortBy", new Document("time", 1))
					.append("output", new Document("prevProduct", new Document("$shift",
							new Document("output", "$productmodel").append("by", -1).append("default", null))))));

		// 5. 生成连续产品组ID
		pipeline.add(new Document("$setWindowFields", new Document("partitionBy", null)
			.append("sortBy", new Document("time", 1))
			.append("output", new Document("groupId", new Document("$sum",
					new Document("$cond",
							Arrays.asList(new Document("$ne", Arrays.asList("$productmodel", "$prevProduct")), 1, 0)))
				.append("window", new Document("documents", Arrays.asList("unbounded", "current")))))));

		// 6. 移除prevProduct字段
		pipeline.add(new Document("$project", new Document("prevProduct", 0)));

		// 7. 合并运行和停止时间
		pipeline.add(
				new Document("$addFields",
						new Document()
							.append("addruntime",
									new Document("$add", Arrays.asList("$mountingcta", "$mountingctb", "$mountingctc",
											"$mountingctd", "$transferct", "$markrecognitioncta", "$markrecognitionctb",
											"$markrecognitionctc", "$markrecognitionctd")))
							.append("addstoptime",
									new Document("$add",
											Arrays.asList("$standbyct", "$errorshutdowntime", "$errorrecoverytime",
													"$awaitingothertracktime", "$operatorshutdowntime",
													"$otherconveyingtabletime")))));

		// 8. 按生产批次分组取关键指标
		pipeline.add(new Document("$group",
				new Document("_id",
						new Document().append("productmodel", "$productmodel")
							.append("groupId", "$groupId")
							.append("batchsequencenumber", "$batchsequencenumber"))
					.append("runtime", new Document("$max", "$addruntime"))
					.append("stoptime", new Document("$max", "$addstoptime"))
					.append("completedpanelcount", new Document("$last", "$completedpanelcount"))
					.append("hour", new Document("$last", "$hour"))));

		// 9. 按小时和产品型号汇总
		pipeline.add(new Document("$group", new Document("_id", new Document().append("hour", "$hour"))
			.append("moduleTotal", new Document("$sum", "$completedpanelcount"))));

		// 执行聚合查询
		List<Document> results = mongoTemplate.getCollection(collectionName)
			.aggregate(pipeline)
			.into(new ArrayList<>());

		// 转换结果为HourlyOutput对象列表
		List<HourlyOutput> hourlyOutputs = new ArrayList<>();
		for (Document doc : results) {
			Document idDoc = (Document) doc.get("_id");
			HourlyOutput hourlyOutput = new HourlyOutput();
			hourlyOutput.setTime(idDoc.getDate("hour").toString());
			hourlyOutput.setCount(doc.getInteger("moduleTotal"));
			hourlyOutputs.add(hourlyOutput);
		}

		return hourlyOutputs;
	}

	public static List<HourlyOutput> getYamahaHourlyOutput(Device device, AnalyzeQuery query,
			MongoTemplate mongoTemplate) {
		// 获取生产日志集合
		String collectionName = OeeUtil.getProductionLogCollection(device.getCode());

		// 构建聚合管道
		List<Document> pipeline = new ArrayList<>();

		// 1. 匹配时间范围和产品型号（如果有）
		Document matchDoc = new Document("time",
				new Document("$gte", query.getStartTime()).append("$lte", query.getEndTime()));
		if (query.getProductModel() != null && !query.getProductModel().isEmpty()) {
			matchDoc.append("productmodel", query.getProductModel());
		}
		pipeline.add(new Document("$match", matchDoc));

		// 2. 按时间排序
		pipeline.add(new Document("$sort", new Document("time", 1)));

		// 3. 计算所属小时（带时区）
		pipeline.add(new Document("$addFields", new Document("hour", new Document("$dateTrunc",
				new Document("date", "$time").append("unit", "hour").append("timezone", "Asia/Shanghai")))));

		// 4. 确定相邻产品变化
		pipeline.add(new Document("$setWindowFields",
				new Document("partitionBy", null).append("sortBy", new Document("time", 1))
					.append("output", new Document("prevProduct", new Document("$shift",
							new Document("output", "$productmodel").append("by", -1).append("default", null))))));

		// 5. 生成连续产品组ID
		pipeline.add(new Document("$setWindowFields", new Document("partitionBy", null)
			.append("sortBy", new Document("time", 1))
			.append("output", new Document("groupId", new Document("$sum",
					new Document("$cond",
							Arrays.asList(new Document("$ne", Arrays.asList("$productmodel", "$prevProduct")), 1, 0)))
				.append("window", new Document("documents", Arrays.asList("unbounded", "current")))))));

		// 6. 移除prevProduct字段
		pipeline.add(new Document("$project", new Document("prevProduct", 0)));

		// 7. 合并运行和停止时间
		pipeline.add(
				new Document("$addFields",
						new Document()
							.append("addruntime",
									new Document("$add", Arrays.asList("$mountingcta", "$mountingctb", "$mountingctc",
											"$mountingctd", "$transferct", "$markrecognitioncta", "$markrecognitionctb",
											"$markrecognitionctc", "$markrecognitionctd")))
							.append("addstoptime",
									new Document("$add",
											Arrays.asList("$standbyct", "$errorshutdowntime", "$errorrecoverytime",
													"$awaitingothertracktime", "$operatorshutdowntime",
													"$otherconveyingtabletime")))));

		// 8. 按生产批次分组取关键指标
		pipeline.add(new Document("$group",
				new Document("_id",
						new Document().append("productmodel", "$productmodel")
							.append("groupId", "$groupId")
							.append("batchsequencenumber", "$batchsequencenumber"))
					.append("runtime", new Document("$max", "$addruntime"))
					.append("stoptime", new Document("$max", "$addstoptime"))
					.append("completedpanelcount", new Document("$last", "$completedpanelcount"))
					.append("hour", new Document("$last", "$hour"))));

		// 9. 按小时和产品型号汇总
		pipeline.add(new Document("$group",
				new Document("_id", new Document().append("productmodel", "$_id.productmodel").append("hour", "$hour"))
					.append("moduleTotal", new Document("$sum", "$completedpanelcount"))));

		// 8. 按时间和产品型号排序
		pipeline.add(new Document("$sort", new Document("_id.hour", 1)// 在同一小时内按照最早时间排序
			.append("_id.productmodel", 1)));

		// 执行聚合查询
		List<Document> results = mongoTemplate.getCollection(collectionName)
			.aggregate(pipeline)
			.into(new ArrayList<>());

		// 转换结果为HourlyOutput对象列表
		List<HourlyOutput> hourlyOutputs = new ArrayList<>();
		for (Document doc : results) {
			Document idDoc = (Document) doc.get("_id");
			HourlyOutput hourlyOutput = new HourlyOutput();
			hourlyOutput.setTime(idDoc.getDate("hour").toString());
			hourlyOutput.setCount(doc.getInteger("moduleTotal"));
			hourlyOutputs.add(hourlyOutput);
		}

		return hourlyOutputs;
	}

}
