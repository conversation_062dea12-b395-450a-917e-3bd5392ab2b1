package com.github.cret.web.common.util;

import java.util.concurrent.Callable;

import org.springframework.boot.json.JsonParseException;

import com.fasterxml.jackson.core.JacksonException;
import com.fasterxml.jackson.databind.ObjectMapper;

/**
 * jackson解析工具类
 */
public class JacksonUtil {

	// 静态代码块单例
	private final static ObjectMapper OBJECT_MAPPER;

	static {
		OBJECT_MAPPER = new ObjectMapper();
	}

	private JacksonUtil() {
	}

	public static ObjectMapper getObjectMapper() {
		return OBJECT_MAPPER;
	}

	public static <T> T tryParse(Callable<T> parser) {
		return tryParse(parser, JacksonException.class);
	}

	public static <T> T tryParse(Callable<T> parser, Class<? extends Exception> check) {
		try {
			return parser.call();
		}
		catch (Exception ex) {
			if (check.isAssignableFrom(ex.getClass())) {
				throw new JsonParseException(ex);
			}
			throw new IllegalStateException(ex);
		}
	}

}
