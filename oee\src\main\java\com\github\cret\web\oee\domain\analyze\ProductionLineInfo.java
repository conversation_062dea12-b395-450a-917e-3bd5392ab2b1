package com.github.cret.web.oee.domain.analyze;

import com.github.cret.web.oee.enums.ProductionSample;

/**
 * 产品信息
 */
public class ProductionLineInfo {

	// 产品型号
	private String productModel;

	// 原产品型号
	private String originalProductModel;

	private String track;

	// 所属轨道
	private String trackName;

	// 是否为样品
	private ProductionSample sample;

	public String getProductModel() {
		return productModel;
	}

	public void setProductModel(String productModel) {
		this.productModel = productModel;
	}

	public String getOriginalProductModel() {
		return originalProductModel;
	}

	public void setOriginalProductModel(String originalProductModel) {
		this.originalProductModel = originalProductModel;
	}

	public String getTrackName() {
		return trackName;
	}

	public void setTrackName(String trackName) {
		this.trackName = trackName;
	}

	public ProductionSample getSample() {
		return sample;
	}

	public void setSample(ProductionSample sample) {
		this.sample = sample;
	}

	public String getTrack() {
		return track;
	}

	public void setTrack(String track) {
		this.track = track;
	}

}
