
package com.github.cret.web.oee.document.mes;

import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

/**
 * 标签最新状态
 */
@Document("t_mes_lable_latest_status")
public class LabelLatestStatus {

	@Id
	private String id;

	@Field(name = "lbId")
	private String lbId;

	@Field(name = "latestIsPass")
	private String latestIsPass;

	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	}

	public String getLbId() {
		return lbId;
	}

	public void setLbId(String lbId) {
		this.lbId = lbId;
	}

	public String getLatestIsPass() {
		return latestIsPass;
	}

	public void setLatestIsPass(String latestIsPass) {
		this.latestIsPass = latestIsPass;
	}

}
