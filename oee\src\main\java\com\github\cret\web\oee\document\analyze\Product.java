package com.github.cret.web.oee.document.analyze;

import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

import com.alibaba.excel.annotation.ExcelProperty;

@Document(collection = "t_product")
public class Product {

	@Id
	private String id;

	/**
	 * 线体编码
	 */
	@ExcelProperty(value = "线体编码")
	@Field(name = "lineCode")
	private String lineCode;

	/**
	 * 产品型号
	 */
	@ExcelProperty(value = "产品型号")
	@Field(name = "productModel")
	private String productModel;

	/**
	 * 产品型号
	 */
	@ExcelProperty(value = "点数")
	@Field(name = "pointNum")
	private Integer pointNum;

	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	}

	public String getLineCode() {
		return lineCode;
	}

	public void setLineCode(String lineCode) {
		this.lineCode = lineCode;
	}

	public String getProductModel() {
		return productModel;
	}

	public void setProductModel(String productModel) {
		this.productModel = productModel;
	}

	public Integer getPointNum() {
		return pointNum;
	}

	public void setPointNum(Integer pointNum) {
		this.pointNum = pointNum;
	}

}
