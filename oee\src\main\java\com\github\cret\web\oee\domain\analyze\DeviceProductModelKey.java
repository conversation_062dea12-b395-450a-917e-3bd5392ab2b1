package com.github.cret.web.oee.domain.analyze;

import java.util.Objects;

public class DeviceProductModelKey {

	private String deviceCode;

	private String productModel;

	public DeviceProductModelKey(String deviceCode, String productModel) {
		this.deviceCode = deviceCode;
		this.productModel = productModel;
	}

	public String getDeviceCode() {
		return deviceCode;
	}

	public void setDeviceCode(String deviceCode) {
		this.deviceCode = deviceCode;
	}

	public String getProductModel() {
		return productModel;
	}

	public void setProductModel(String productModel) {
		this.productModel = productModel;
	}

	// 需要实现equals和hashCode方法以保证Map的正确使用
	@Override
	public boolean equals(Object o) {
		if (this == o)
			return true;
		if (o == null || getClass() != o.getClass())
			return false;
		DeviceProductModelKey that = (DeviceProductModelKey) o;
		return Objects.equals(deviceCode, that.deviceCode) && Objects.equals(productModel, that.productModel);
	}

	@Override
	public int hashCode() {
		return Objects.hash(deviceCode, productModel);
	}

}
