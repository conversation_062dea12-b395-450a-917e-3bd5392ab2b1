package com.github.cret.web.oee.domain.wxwork;

import com.github.cret.web.oee.config.WxWorkConfig;

// BaseReq 是企业微信消息请求的基础参数类。
// 用于封装发送消息时的通用参数。
public class BaseReq {

	// 接收者成员ID列表（多个用|分隔，最多1000个）
	private String touser;

	// 接收者部门ID列表（多个用|分隔，最多100个）
	private String toparty;

	// 接收者标签ID列表（多个用|分隔，最多100个）
	private String totag;

	// 消息类型，如 text、image、markdown 等
	private String msgtype;

	// 企业应用的 agentid
	private int agentid;

	// 是否开启重复消息检查，0表示否，1表示是
	Integer enable_duplicate_check;

	// 重复消息检查的时间间隔，单位为秒，最大不超过4小时
	Integer duplicate_check_interval;

	// 默认构造方法
	BaseReq() {

	}

	// 带参数的构造方法，便于通过 WxWorkConfig 初始化
	BaseReq(String toUser, String toParty, String toTag, String msgType, WxWorkConfig wxWorkConfig) {
		this.touser = toUser;
		this.toparty = toParty;
		this.totag = toTag;
		this.msgtype = msgType;
		this.agentid = wxWorkConfig.getAgentId();
		this.enable_duplicate_check = wxWorkConfig.getEnableDuplicateCheck();
		this.duplicate_check_interval = wxWorkConfig.getDuplicateCheckInterval();
	}

	public String getTouser() {
		return touser;
	}

	public void setTouser(String touser) {
		this.touser = touser;
	}

	public String getToparty() {
		return toparty;
	}

	public void setToparty(String toparty) {
		this.toparty = toparty;
	}

	public String getTotag() {
		return totag;
	}

	public void setTotag(String totag) {
		this.totag = totag;
	}

	public String getMsgtype() {
		return msgtype;
	}

	public void setMsgtype(String msgtype) {
		this.msgtype = msgtype;
	}

	public int getAgentid() {
		return agentid;
	}

	public void setAgentid(int agentid) {
		this.agentid = agentid;
	}

	public Integer getEnable_duplicate_check() {
		return enable_duplicate_check;
	}

	public void setEnable_duplicate_check(Integer enable_duplicate_check) {
		this.enable_duplicate_check = enable_duplicate_check;
	}

	public Integer getDuplicate_check_interval() {
		return duplicate_check_interval;
	}

	public void setDuplicate_check_interval(Integer duplicate_check_interval) {
		this.duplicate_check_interval = duplicate_check_interval;
	}

}
