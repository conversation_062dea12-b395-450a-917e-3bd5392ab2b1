package com.github.cret.web.oee.controller;

import java.util.List;

import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.github.cret.web.common.domain.PageList;
import com.github.cret.web.common.domain.PageableParam;
import com.github.cret.web.oee.document.feedback.FeedbackTriggerSolutionSend;
import com.github.cret.web.oee.service.FeedbackTriggerSolutionSendService;
import com.github.cret.web.oee.service.FeedbackTriggerSolutionService;

@RestController
@RequestMapping("/feedback-trigger-solution-send")
public class FeedbackTriggerSolutionSendController {

	private final FeedbackTriggerSolutionSendService feedbackTriggerSolutionSendService;

	private final FeedbackTriggerSolutionService feedbackTriggerSolutionService;

	public FeedbackTriggerSolutionSendController(FeedbackTriggerSolutionSendService feedbackTriggerSolutionSendService,
			FeedbackTriggerSolutionService feedbackTriggerSolutionService) {
		this.feedbackTriggerSolutionSendService = feedbackTriggerSolutionSendService;
		this.feedbackTriggerSolutionService = feedbackTriggerSolutionService;
	}

	// ==================== 基础CRUD操作 ====================

	@PostMapping
	public FeedbackTriggerSolutionSend create(@RequestBody FeedbackTriggerSolutionSend solutionSend) {
		return feedbackTriggerSolutionSendService.save(solutionSend);
	}

	@GetMapping("/{id}")
	public FeedbackTriggerSolutionSend getById(@PathVariable String id) {
		return feedbackTriggerSolutionSendService.findById(id);
	}

	@PostMapping("/page")
	public PageList<FeedbackTriggerSolutionSend> page(@RequestBody PageableParam<FeedbackTriggerSolutionSend> param) {
		return feedbackTriggerSolutionSendService.page(param);
	}

	@PostMapping("/list")
	public List<FeedbackTriggerSolutionSend> list(@RequestBody FeedbackTriggerSolutionSend param) {
		return feedbackTriggerSolutionSendService.findList(param);
	}

	@GetMapping
	public List<FeedbackTriggerSolutionSend> getAll() {
		return feedbackTriggerSolutionSendService.findAll();
	}

	@PutMapping("/{id}")
	public FeedbackTriggerSolutionSend update(@PathVariable String id,
			@RequestBody FeedbackTriggerSolutionSend solutionSend) {
		solutionSend.setId(id);
		return feedbackTriggerSolutionSendService.save(solutionSend);
	}

	@DeleteMapping("/{id}")
	public void delete(@PathVariable String id) {
		feedbackTriggerSolutionSendService.deleteById(id);
	}

	@DeleteMapping("/batch")
	public void batchDelete(@RequestBody List<String> ids) {
		feedbackTriggerSolutionSendService.batchDelete(ids);
	}

	// ==================== 业务查询接口 ====================

	@GetMapping("/by-solution/{solutionId}")
	public List<FeedbackTriggerSolutionSend> getBySolutionId(@PathVariable String solutionId) {
		return feedbackTriggerSolutionSendService.findByTriggerSolutionId(solutionId);
	}

	@GetMapping("/by-status")
	public List<FeedbackTriggerSolutionSend> getByStatus(@RequestParam Boolean sendStatus) {
		return feedbackTriggerSolutionSendService.findBySendStatus(sendStatus);
	}

	@GetMapping("/by-send-user/{userId}")
	public List<FeedbackTriggerSolutionSend> getBySendUserId(@PathVariable String userId) {
		return feedbackTriggerSolutionSendService.findBySendUserId(userId);
	}

	@GetMapping("/by-report-user/{userId}")
	public List<FeedbackTriggerSolutionSend> getByReportUserId(@PathVariable String userId) {
		return feedbackTriggerSolutionSendService.findByReportUserId(userId);
	}

	// ==================== 状态更新接口 ====================

	@PutMapping("/{id}/status")
	public FeedbackTriggerSolutionSend updateSendStatus(@PathVariable String id, @RequestParam Boolean sendStatus,
			@RequestParam(required = false) String sendResult) {
		return feedbackTriggerSolutionSendService.updateSendStatus(id, sendStatus, sendResult);
	}

	@PutMapping("/{id}/mark-sent")
	public FeedbackTriggerSolutionSend markAsSent(@PathVariable String id,
			@RequestParam(required = false) String sendResult) {
		return feedbackTriggerSolutionSendService.markAsSent(id, sendResult);
	}

	@PutMapping("/{id}/mark-failed")
	public FeedbackTriggerSolutionSend markAsFailed(@PathVariable String id,
			@RequestParam(required = false) String sendResult) {
		return feedbackTriggerSolutionSendService.markAsFailed(id, sendResult);
	}

	// ==================== 业务操作接口 ====================

	@PostMapping("/{id}/perform-send")
	public String performSend(@PathVariable String id) {
		FeedbackTriggerSolutionSend sendRecord = feedbackTriggerSolutionSendService.findById(id);
		return feedbackTriggerSolutionSendService.performSend(sendRecord);
	}

	@PostMapping("/process-scheduled-sends")
	public void processScheduledSends() {
		feedbackTriggerSolutionSendService.processScheduledSends();
	}

	@PostMapping("/create-notification")
	public java.util.List<FeedbackTriggerSolutionSend> createNotification(@RequestParam String solutionId,
			@RequestBody java.util.List<com.github.cret.web.oee.document.feedback.NoticeUser> noticeUsers) {
		// 根据solutionId获取FeedbackTriggerSolution对象
		com.github.cret.web.oee.document.feedback.FeedbackTriggerSolution solution = feedbackTriggerSolutionService
			.findById(solutionId);
		return feedbackTriggerSolutionSendService.createSolutionNotification(solution, noticeUsers);
	}

	@PostMapping("/create-reject-notification")
	public FeedbackTriggerSolutionSend createRejectNotification(@RequestParam String solutionId) {
		// 根据solutionId获取FeedbackTriggerSolution对象
		com.github.cret.web.oee.document.feedback.FeedbackTriggerSolution solution = feedbackTriggerSolutionService
			.findById(solutionId);
		return feedbackTriggerSolutionSendService.createRejectNotification(solution);
	}

}
