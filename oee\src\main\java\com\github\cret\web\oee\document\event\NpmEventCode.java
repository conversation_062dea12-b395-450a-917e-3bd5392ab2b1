package com.github.cret.web.oee.document.event;

import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

import com.alibaba.excel.annotation.ExcelProperty;
import com.github.cret.web.oee.enums.EventType;

/**
 * 松下贴片机事件代码
 */
@Document(collection = "npm_event_code")
public class NpmEventCode {

	@Id
	@ExcelProperty(value = "ID")
	private String id;

	@Field(name = "maincode")
	private String mainCode;

	@Field(name = "subcode")
	private String subCode;

	@Field(name = "mainsubject")
	private String mainSubject;

	@Field(name = "subsubject")
	private String subSubject;

	@Field(name = "eventtype")
	private EventType eventType;

	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	}

	public String getMainCode() {
		return mainCode;
	}

	public void setMainCode(String mainCode) {
		this.mainCode = mainCode;
	}

	public String getSubCode() {
		return subCode;
	}

	public void setSubCode(String subCode) {
		this.subCode = subCode;
	}

	public String getMainSubject() {
		return mainSubject;
	}

	public void setMainSubject(String mainSubject) {
		this.mainSubject = mainSubject;
	}

	public String getSubSubject() {
		return subSubject;
	}

	public void setSubSubject(String subSubject) {
		this.subSubject = subSubject;
	}

	public EventType getEventType() {
		return eventType;
	}

	public void setEventType(EventType eventType) {
		this.eventType = eventType;
	}

}
