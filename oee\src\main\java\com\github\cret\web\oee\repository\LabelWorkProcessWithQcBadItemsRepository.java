package com.github.cret.web.oee.repository;

import java.util.Date;
import java.util.List;
import java.util.Optional;

import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.stereotype.Repository;

import com.github.cret.web.oee.document.mes.LabelWorkProcessWithQcBadItemsDoc;

@Repository
public interface LabelWorkProcessWithQcBadItemsRepository
		extends MongoRepository<LabelWorkProcessWithQcBadItemsDoc, String> {

	/**
	 * 根据线体ID查询组合数据
	 * @param plId 线体ID
	 * @return 组合数据列表
	 */
	List<LabelWorkProcessWithQcBadItemsDoc> findByPlId(String plId);

	/**
	 * 根据线体ID和时间范围查询组合数据
	 * @param plId 线体ID
	 * @param startDate 开始时间
	 * @param endDate 结束时间
	 * @return 组合数据列表
	 */
	List<LabelWorkProcessWithQcBadItemsDoc> findByPlIdAndFinalWpCmpDateBetween(String plId, Date startDate,
			Date endDate);

	/**
	 * 根据标签ID和线体ID查找唯一记录
	 * @param lbId 标签ID
	 * @param plId 线体ID
	 * @return 组合数据
	 */
	Optional<LabelWorkProcessWithQcBadItemsDoc> findByLbIdAndPlId(String lbId, String plId);

	/**
	 * 根据线体ID和制造订单号查询
	 * @param plId 线体ID
	 * @param mo 制造订单号
	 * @return 组合数据列表
	 */
	List<LabelWorkProcessWithQcBadItemsDoc> findByPlIdAndMo(String plId, String mo);

	/**
	 * 删除指定线体和时间范围的数据
	 * @param plId 线体ID
	 * @param startDate 开始时间
	 * @param endDate 结束时间
	 */
	void deleteByPlIdAndFinalWpCmpDateBetween(String plId, Date startDate, Date endDate);

	/**
	 * 根据标签ID列表查询
	 * @param lbIds 标签ID列表
	 * @return 组合数据列表
	 */
	List<LabelWorkProcessWithQcBadItemsDoc> findByLbIdIn(List<String> lbIds);

	/**
	 * 根据线体ID和关键工序首次结果查询
	 * @param plId 线体ID
	 * @param keyWpFirstResult 关键工序首次结果
	 * @return 组合数据列表
	 */
	List<LabelWorkProcessWithQcBadItemsDoc> findByPlIdAndKeyWpFirstResult(String plId, String keyWpFirstResult);

	/**
	 * 根据线体ID和最终结果查询
	 * @param plId 线体ID
	 * @param finalResult 最终结果
	 * @return 组合数据列表
	 */
	List<LabelWorkProcessWithQcBadItemsDoc> findByPlIdAndFinalResult(String plId, String finalResult);

}
