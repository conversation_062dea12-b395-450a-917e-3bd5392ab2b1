package com.github.cret.web.oee.service.impl;

import com.github.cret.web.common.domain.PageList;
import com.github.cret.web.common.domain.PageableParam;
import com.github.cret.web.oee.document.TDeviceConfig;
import com.github.cret.web.oee.document.analyze.Abnormal;
import com.github.cret.web.oee.repository.TDeviceConfigRepository;
import com.github.cret.web.oee.service.TDeviceConfigService;
import jakarta.annotation.Resource;
import org.springframework.data.domain.Limit;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.List;

@Service
public class TDeviceConfigServiceImpl implements TDeviceConfigService {

	@Resource
	private TDeviceConfigRepository tDeviceConfigRepository;

	@Resource
	private MongoTemplate mongoTemplate;

	@Override
	public void save(TDeviceConfig tDeviceConfig) {
		tDeviceConfigRepository.save(tDeviceConfig);
	}

	@Override
	public void deleteById(String id) {
		tDeviceConfigRepository.deleteById(id);
	}

	@Override
	public TDeviceConfig findById(String id) {
		return tDeviceConfigRepository.findById(id).orElse(null);
	}

	@Override
	public PageList<TDeviceConfig> page(PageableParam<String> param) {

		PageList<TDeviceConfig> pageList = new PageList<>();
		String deviceCode = param.getSearchParams();

		Criteria criteria = new Criteria();
		Integer pageSize = param.getPageData().getPageSize();
		Integer pageNum = param.getPageData().getPageNumber();

		// 模糊查询设备号
		if (StringUtils.hasText(deviceCode)) {
			criteria.and("deviceCode").regex(".*" + deviceCode + ".*");
		}

		// 设置默认排序字段
		if (param.getPageData().getSortField() == null) {
			param.getPageData().setSortField("deviceCode");
		}

		Query query = Query.query(criteria);
		query.with(param.getPageRequest());

		long total = mongoTemplate.count(query.skip(0).limit(0), Abnormal.class);
		List<TDeviceConfig> content = mongoTemplate
			.find(query.skip((long) pageNum * pageSize).limit(Limit.of(pageSize)), TDeviceConfig.class);

		pageList.setTotal(total);
		pageList.setList(content);

		// 计算是否有下一页
		int currentPage = param.getPageRequest().getPageNumber();
		boolean hasNext = (long) (currentPage + 1) * pageSize < total;
		pageList.setHasNext(hasNext);

		return pageList;
	}

}
