package com.github.cret.web.oee.service.impl;

import java.util.List;

import org.springframework.data.domain.Example;
import org.springframework.data.domain.ExampleMatcher;
import org.springframework.stereotype.Service;

import com.github.cret.web.common.domain.PageList;
import com.github.cret.web.common.domain.PageableParam;
import com.github.cret.web.oee.document.analyze.TargetOee;
import com.github.cret.web.oee.repository.TargetOeeRepository;
import com.github.cret.web.oee.service.TargetOeeService;

@Service
public class TargetOeeServiceImpl implements TargetOeeService {

	private final TargetOeeRepository repository;

	public TargetOeeServiceImpl(TargetOeeRepository repository) {
		this.repository = repository;
	}

	@Override
	public TargetOee save(TargetOee targetOee) {
		return repository.save(targetOee);
	}

	@Override
	public TargetOee findById(String id) {
		return repository.findById(id).orElse(null);
	}

	@Override
	public List<TargetOee> findAll() {
		return repository.findAll();
	}

	@Override
	public void deleteById(String id) {
		repository.deleteById(id);
	}

	@Override
	public PageList<TargetOee> page(PageableParam<TargetOee> param) {
		ExampleMatcher matcher = ExampleMatcher.matching().withIgnoreNullValues();
		Example<TargetOee> example = Example.of(param.getSearchParams(), matcher);
		return PageList.fromPage(repository.findAll(example, param.getPageRequest()));
	}

	@Override
	public TargetOee getTargetOeeOrDefault() {
		List<TargetOee> targetOeeList = repository.findAll();
		if (!targetOeeList.isEmpty()) {
			return targetOeeList.get(0);
		}
		else {
			TargetOee defaultTarget = new TargetOee();
			defaultTarget.setAvailability(1.0);
			defaultTarget.setPerformance(1.0);
			defaultTarget.setQuality(1.0);
			return defaultTarget;
		}
	}

}
