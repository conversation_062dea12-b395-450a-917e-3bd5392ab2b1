package com.github.cret.web.oee.domain.analyze;

import java.util.Date;

public class LineChangeoverBlock {

	private int groupId;

	private Date startTime;

	private Date endTime;

	private long timeDifference;

	private int difference;

	public int getGroupId() {
		return groupId;
	}

	public void setGroupId(int groupId) {
		this.groupId = groupId;
	}

	public Date getStartTime() {
		return startTime;
	}

	public void setStartTime(Date startTime) {
		this.startTime = startTime;
	}

	public Date getEndTime() {
		return endTime;
	}

	public void setEndTime(Date endTime) {
		this.endTime = endTime;
	}

	public long getTimeDifference() {
		return timeDifference;
	}

	public void setTimeDifference(long timeDifference) {
		this.timeDifference = timeDifference;
	}

	public int getDifference() {
		return difference;
	}

	public void setDifference(int difference) {
		this.difference = difference;
	}

}
