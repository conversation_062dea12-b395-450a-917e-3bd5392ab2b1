package com.github.cret.web.oee.service.impl;

import java.util.List;

import com.github.cret.web.oee.domain.analyze.ActualProduction;
import com.github.cret.web.oee.domain.analyze.AnalyzeQuery;
import com.github.cret.web.oee.service.ProductModelService;

public class ProductModelServiceImpl implements ProductModelService {

	@Override
	public List<ActualProduction> getActualProduction(AnalyzeQuery query) {
		// TODO Auto-generated method stub
		throw new UnsupportedOperationException("Unimplemented method 'getActualProduction'");
	}

}
