package com.github.cret.web.oee.domain.analyze;

import java.util.List;

/**
 * 看板分析结果
 */
public class AnalyzeResult {

	/**
	 * 生产线信息
	 */
	ProductionLineResult productionLineResult;

	/**
	 * OEE指标数据
	 */
	OeeResult oeeResult;

	/**
	 * 设备CT数据
	 */
	List<DeviceCtInfo> deviceCtInfos;

	/**
	 * 直通不良类型数据
	 * @return
	 */
	DefectTypeResult firstPassDefectTypes;

	/**
	 * 复判不良类型数据
	 */
	DefectTypeResult retrialDefectTypes;

	public OeeResult getOeeResult() {
		return oeeResult;
	}

	public void setOeeResult(OeeResult oeeResult) {
		this.oeeResult = oeeResult;
	}

	public List<DeviceCtInfo> getDeviceCtInfos() {
		return deviceCtInfos;
	}

	public void setDeviceCtInfos(List<DeviceCtInfo> deviceCtInfos) {
		this.deviceCtInfos = deviceCtInfos;
	}

	public DefectTypeResult getFirstPassDefectTypes() {
		return firstPassDefectTypes;
	}

	public void setFirstPassDefectTypes(DefectTypeResult firstPassDefectTypes) {
		this.firstPassDefectTypes = firstPassDefectTypes;
	}

	public DefectTypeResult getRetrialDefectTypes() {
		return retrialDefectTypes;
	}

	public void setRetrialDefectTypes(DefectTypeResult retrialDefectTypes) {
		this.retrialDefectTypes = retrialDefectTypes;
	}

	public ProductionLineResult getProductionLineResult() {
		return productionLineResult;
	}

	public void setProductionLineResult(ProductionLineResult productionLineResult) {
		this.productionLineResult = productionLineResult;
	}

}
