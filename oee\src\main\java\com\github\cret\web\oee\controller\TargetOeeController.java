package com.github.cret.web.oee.controller;

import java.util.List;

import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.github.cret.web.common.domain.PageList;
import com.github.cret.web.common.domain.PageableParam;
import com.github.cret.web.oee.document.analyze.TargetOee;
import com.github.cret.web.oee.service.TargetOeeService;

/**
 * 目标OEE控制器
 */
@RestController
@RequestMapping("/target-oee")
public class TargetOeeController {

	private final TargetOeeService targetOeeService;

	public TargetOeeController(TargetOeeService targetOeeService) {
		this.targetOeeService = targetOeeService;
	}

	@GetMapping("/")
	public List<TargetOee> findAll() {
		return targetOeeService.findAll();
	}

	@PostMapping
	public TargetOee save(@RequestBody TargetOee targetOee) {
		return targetOeeService.save(targetOee);
	}

	@PutMapping("/{id}")
	public TargetOee update(@PathVariable String id, @RequestBody TargetOee targetOee) {
		targetOee.setId(id);
		return targetOeeService.save(targetOee);
	}

	@DeleteMapping("/{id}")
	public void deleteById(@PathVariable String id) {
		targetOeeService.deleteById(id);
	}

	@GetMapping("/{id}")
	public TargetOee findById(@PathVariable String id) {
		return targetOeeService.findById(id);
	}

	@PostMapping("/page")
	public PageList<TargetOee> page(@RequestBody PageableParam<TargetOee> param) {
		return targetOeeService.page(param);
	}

}
