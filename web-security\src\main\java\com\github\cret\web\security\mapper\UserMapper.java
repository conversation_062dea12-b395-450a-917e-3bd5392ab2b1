package com.github.cret.web.security.mapper;

import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingConstants;
import org.mapstruct.MappingTarget;
import org.mapstruct.ReportingPolicy;

import com.github.cret.web.security.document.User;
import com.github.cret.web.security.domain.AuthUser;
import com.github.cret.web.security.domain.UserCreate;
import com.github.cret.web.security.domain.UserQuery;
import com.github.cret.web.security.domain.UserUpdate;
import com.github.cret.web.security.domain.UserView;

@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE, componentModel = MappingConstants.ComponentModel.SPRING)
public interface UserMapper {

	User toEntity(UserCreate userCreate);

	User toEntity(UserQuery userQuery);

	AuthUser toAuthUser(User User);

	UserView toView(User user);

	@Mapping(target = "id", ignore = true)
	void partialUpdate(UserUpdate userUpdate, @MappingTarget User user);

}
