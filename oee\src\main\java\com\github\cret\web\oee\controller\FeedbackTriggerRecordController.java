package com.github.cret.web.oee.controller;

import com.github.cret.web.common.domain.PageList;
import com.github.cret.web.oee.document.feedback.FeedbackTriggerRecord;
import com.github.cret.web.oee.document.feedback.NoticeUser;
import com.github.cret.web.oee.domain.query.FeedbackTriggerRecordQuery;
import com.github.cret.web.oee.service.FeedbackTriggerRecordService;
import com.github.cret.web.system.domain.DictItem;
import com.github.cret.web.system.service.DictService;

import java.util.List;

import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/feedback-trigger-records")
public class FeedbackTriggerRecordController {

	private final FeedbackTriggerRecordService service;

	private final DictService dictService;

	public FeedbackTriggerRecordController(FeedbackTriggerRecordService service, DictService dictService) {
		this.service = service;
		this.dictService = dictService;
	}

	@PostMapping
	public FeedbackTriggerRecord create(@RequestBody FeedbackTriggerRecord record) {
		return service.save(record);
	}

	@DeleteMapping("/{id}")
	public void delete(@PathVariable String id) {
		service.delete(id);
	}

	@GetMapping("/{id}")
	public FeedbackTriggerRecord getById(@PathVariable String id) {
		return service.findById(id);
	}

	@PostMapping("/page")
	public PageList<FeedbackTriggerRecord> search(@RequestBody FeedbackTriggerRecordQuery query) {
		return service.search(query);
	}

	@GetMapping("/has-open-exceptions/{lineCode}")
	public boolean hasOpenExceptions(@PathVariable String lineCode) {
		return service.hasOpenExceptions(lineCode);
	}

	@PutMapping("/{id}")
	public FeedbackTriggerRecord update(@PathVariable String id, @RequestBody FeedbackTriggerRecord record) {
		return service.update(id, record);
	}

	/**
	 * 关闭异常并记录关闭时间
	 * @param id 记录ID
	 * @return 更新后的记录
	 */
	@PutMapping("/{id}/close")
	public FeedbackTriggerRecord closeException(@PathVariable String id) {
		return service.closeException(id);
	}

	/**
	 * 批量关闭异常并记录关闭时间
	 * @param ids 记录ID列表
	 * @return 成功关闭的记录数量
	 */
	@PostMapping("/batch-close")
	public int batchCloseException(@RequestBody List<String> ids) {
		return service.batchCloseException(ids);
	}

	/**
	 * 获取线体最新的未关闭的异常
	 * @param lineCode
	 * @return
	 */
	@GetMapping("/latest-open-exception/{lineCode}")
	public FeedbackTriggerRecord getLatestOpenException(@PathVariable String lineCode) {
		// 获取异常大类字典项列表
		List<DictItem> abnormalCategories = dictService.listItem("ABNORMAL_CATEGORY");

		// 获取最新的未关闭异常记录
		FeedbackTriggerRecord record = service.findLatestOpenException(lineCode);

		if (record != null && abnormalCategories != null) {
			// 遍历字典项，匹配anomaliesCode并设置名称
			abnormalCategories.stream()
				.filter(item -> item.getValue().equals(record.getAnomaliesCode()))
				.findFirst()
				.ifPresent(matchedItem -> record.setAnomaliesName(matchedItem.getLabel()));
		}

		return record;
	}

	/**
	 * 获取触发记录的通知用户列表
	 * @param id 触发记录ID
	 * @return 通知用户列表
	 */
	@GetMapping("/{id}/notice-users")
	public List<NoticeUser> getNoticeUsers(@PathVariable String id) {
		return service.getNoticeUsers(id);
	}

}