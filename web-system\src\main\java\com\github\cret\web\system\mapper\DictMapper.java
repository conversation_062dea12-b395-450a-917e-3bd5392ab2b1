package com.github.cret.web.system.mapper;

import org.mapstruct.Mapper;
import org.mapstruct.MappingConstants;
import org.mapstruct.ReportingPolicy;

import com.github.cret.web.system.document.Dict;
import com.github.cret.web.system.domain.DictCreate;
import com.github.cret.web.system.domain.DictSimple;
import com.github.cret.web.system.domain.DictUpdate;

@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE, componentModel = MappingConstants.ComponentModel.SPRING)
public interface DictMapper {

	Dict toEntity(DictSimple dictSimple);

	DictSimple toSimple(Dict dict);

	Dict toEntity(DictCreate dictCreate);

	Dict toEntity(DictUpdate dictUpdate);

}
