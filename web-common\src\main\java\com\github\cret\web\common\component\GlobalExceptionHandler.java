package com.github.cret.web.common.component;

import com.github.cret.web.common.domain.ErrRes;
import com.github.cret.web.common.enumerate.SysErrEnum;
import com.github.cret.web.common.exception.SystemException;
import com.mongodb.MongoWriteException;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import java.util.NoSuchElementException;

@RestControllerAdvice
public class GlobalExceptionHandler {

	@ExceptionHandler(SystemException.class)
	public ResponseEntity<ErrRes> handleSystemException(SystemException ex) {
		return new ResponseEntity<>(ex.toRes(), ex.getStatus());
	}

	@ExceptionHandler(NoSuchElementException.class)
	public ResponseEntity<ErrRes> handleNoSuchElementException(NoSuchElementException ex) {
		return new ResponseEntity<ErrRes>(SysErrEnum.NOT_FOUND.exception(ex).toRes(),
				SysErrEnum.NOT_FOUND.getHttpStatus());
	}

	@ExceptionHandler(MongoWriteException.class)
	public ResponseEntity<ErrRes> handleMongoWriteException(MongoWriteException ex) {
		if (ex.getError() != null && ex.getError().getCode() == 11000) {
			return new ResponseEntity<ErrRes>(SysErrEnum.CONFLICT_KEY.exception("重复录入").toRes(),
					SysErrEnum.CONFLICT_KEY.getHttpStatus());
		}
		return new ResponseEntity<ErrRes>(SysErrEnum.SYSTEM_ERROR.exception(ex).toRes(),
				SysErrEnum.SYSTEM_ERROR.getHttpStatus());
	}

}
