package com.github.cret.web.system.controller;

import com.github.cret.web.common.annotation.OpLog;
import com.github.cret.web.common.domain.PageList;
import com.github.cret.web.common.domain.PageableParam;
import com.github.cret.web.system.document.MenuItem;
import com.github.cret.web.system.domain.MenuItemCreate;
import com.github.cret.web.system.service.MenuService;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/menu")
public class MenuController {

	private final MenuService menuService;

	public MenuController(MenuService menuService) {
		this.menuService = menuService;
	}

	@PostMapping("/create")
	@OpLog(name = "创建菜单")
	public void create(@RequestBody @Valid MenuItemCreate menuItem) {
		menuService.create(menuItem);

	}

	@PostMapping("/update/{id}")
	@OpLog(name = "更新菜单")
	public void update(@PathVariable("id") @Valid @NotNull String id, @Valid @RequestBody MenuItem menuDataItem) {
		menuService.update(id, menuDataItem);

	}

	@PostMapping("/listItem")
	public List<MenuItem> listItem(@RequestBody MenuItem menuDataItem) {
		return menuService.listItem(menuDataItem);
	}

	@GetMapping("/listChildren/{parentId}")
	public List<MenuItem> listChildren(@PathVariable("parentId") String parentId) {
		return menuService.listChildren(parentId);
	}

	@GetMapping("/getUserMenu")
	public List<MenuItem> getUserMenu() {
		return menuService.getUserMenu();
	}

	@GetMapping("/listRootItem")
	public List<MenuItem> listRootItem() {
		return menuService.listRootItem();
	}

	@PostMapping("/pageItem")
	public PageList<MenuItem> pageItem(@RequestBody PageableParam<MenuItem> searchData) {
		return menuService.pageItem(searchData);
	}

	@DeleteMapping("/{id}")
	public void delete(@PathVariable("id") String id) {
		menuService.delete(id);
	}

	@GetMapping("/{id}")
	public MenuItem get(@PathVariable("id") String id) {
		return menuService.get(id);
	}

}