package com.github.cret.web.oee.domain.print_gkg;

import org.springframework.data.mongodb.core.mapping.Field;

public record PrinterIndex(
		// 楼层
		@Field("floor") String floor,
		// 产线
		@Field("line") String line,
		// 机器类型
		@Field("machinetype") String machineType,
		// 供应商
		@Field("vendor") String vendor,
		// 机器ID
		@Field("machineid") String machineId,
		// 机器序列号
		@Field("machinesn") String machineSn) {

}
