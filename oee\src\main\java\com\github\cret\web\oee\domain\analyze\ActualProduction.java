package com.github.cret.web.oee.domain.analyze;

import com.github.cret.web.oee.enums.DeviceType;

// 实际生产数量
public class ActualProduction {

	// 生产数量
	private Integer num;

	// 设备编码
	private String deviceCode;

	// 设备类型
	private DeviceType deviceType;

	public Integer getNum() {
		return num;
	}

	public void setNum(Integer num) {
		this.num = num;
	}

	public String getDeviceCode() {
		return deviceCode;
	}

	public void setDeviceCode(String deviceCode) {
		this.deviceCode = deviceCode;
	}

	public DeviceType getDeviceType() {
		return deviceType;
	}

	public void setDeviceType(DeviceType deviceType) {
		this.deviceType = deviceType;
	}

}
