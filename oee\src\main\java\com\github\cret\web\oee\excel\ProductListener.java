package com.github.cret.web.oee.excel;

import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.dao.DataIntegrityViolationException;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.read.listener.ReadListener;
import com.alibaba.excel.util.ListUtils;
import com.github.cret.web.oee.document.analyze.Product;
import com.github.cret.web.oee.repository.ProductRepository;

public class ProductListener implements ReadListener<Product> {

	private static final Logger log = LoggerFactory.getLogger(ProductListener.class);

	private static final int BATCH_COUNT = 100;

	private List<Product> cachedDataList = ListUtils.newArrayListWithExpectedSize(BATCH_COUNT);

	private final ProductRepository repository;

	public ProductListener(ProductRepository repository) {
		this.repository = repository;
	}

	@Override
	public void invoke(Product data, AnalysisContext context) {
		cachedDataList.add(data);
		if (cachedDataList.size() >= BATCH_COUNT) {
			saveData();
			cachedDataList = ListUtils.newArrayListWithExpectedSize(BATCH_COUNT);
		}
	}

	@Override
	public void doAfterAllAnalysed(AnalysisContext context) {
		saveData();
		log.info("所有数据解析完成！");
	}

	private void saveData() {
		log.info("{}条数据，开始存储数据库！", cachedDataList.size());
		int successfulCount = 0;
		for (Product product : cachedDataList) {
			try {
				repository.save(product);
				successfulCount++;
			}
			catch (DataIntegrityViolationException e) {
				log.error("数据重复，跳过该条数据：{}", product, e);
			}
		}
		log.info("成功存储{}条数据到数据库！", successfulCount);
	}

}