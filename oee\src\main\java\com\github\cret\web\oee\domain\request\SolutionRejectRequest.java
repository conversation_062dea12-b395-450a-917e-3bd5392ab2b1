package com.github.cret.web.oee.domain.request;

import jakarta.validation.constraints.NotBlank;

/**
 * 解决方案退回请求对象
 */
public class SolutionRejectRequest {

	/**
	 * 退回原因
	 */
	@NotBlank(message = "退回原因不能为空")
	private String rejectReason;

	/**
	 * 退回人ID
	 */
	@NotBlank(message = "退回人ID不能为空")
	private String rejectorId;

	/**
	 * 退回人名称
	 */
	@NotBlank(message = "退回人名称不能为空")
	private String rejectorName;

	public String getRejectReason() {
		return rejectReason;
	}

	public void setRejectReason(String rejectReason) {
		this.rejectReason = rejectReason;
	}

	public String getRejectorId() {
		return rejectorId;
	}

	public void setRejectorId(String rejectorId) {
		this.rejectorId = rejectorId;
	}

	public String getRejectorName() {
		return rejectorName;
	}

	public void setRejectorName(String rejectorName) {
		this.rejectorName = rejectorName;
	}

}
