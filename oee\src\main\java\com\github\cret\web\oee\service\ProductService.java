package com.github.cret.web.oee.service;

import java.io.IOException;
import java.util.List;

import org.springframework.web.multipart.MultipartFile;

import com.github.cret.web.common.domain.PageList;
import com.github.cret.web.common.domain.PageableParam;
import com.github.cret.web.oee.document.analyze.Product;

public interface ProductService {

	// 保存理论产出
	Product save(Product product);

	// 分页查询理论产出
	PageList<Product> page(PageableParam<Product> param);

	// 查询理论产出列表
	List<Product> findList(Product param);

	// 根据id查询理论产出
	Product findById(String id);

	// 查询所有理论产出
	List<Product> findAll();

	// 根据id删除理论产出
	void deleteById(String id);

	// 导入理论产出
	void importExcel(MultipartFile file) throws IOException;

	// 根据产品名称查找产品信息
	Product findOneByProductModel(String productModel);

}
