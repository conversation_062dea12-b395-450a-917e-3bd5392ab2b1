package com.github.cret.web.oee.document;

import com.github.cret.web.oee.domain.deviceConfig.Input;
import com.github.cret.web.oee.domain.deviceConfig.Output;
import com.github.cret.web.oee.domain.deviceConfig.Pipeline;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

@Document(collection = "t_device_config")
public class TDeviceConfig {

	@Id
	private String id;

	@Field(name = "deviceCode")
	private String deviceCode;

	@Field(name = "input")
	private Input input;

	@Field(name = "logType")
	private String logType;

	@Field(name = "output")
	private Output output;

	@Field(name = "pipeline")
	private Pipeline pipeline;

	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	}

	public String getDeviceCode() {
		return deviceCode;
	}

	public void setDeviceCode(String deviceCode) {
		this.deviceCode = deviceCode;
	}

	public Input getInput() {
		return input;
	}

	public void setInput(Input input) {
		this.input = input;
	}

	public String getLogType() {
		return logType;
	}

	public void setLogType(String logType) {
		this.logType = logType;
	}

	public Output getOutput() {
		return output;
	}

	public void setOutput(Output output) {
		this.output = output;
	}

	public Pipeline getPipeline() {
		return pipeline;
	}

	public void setPipeline(Pipeline pipeline) {
		this.pipeline = pipeline;
	}

}
