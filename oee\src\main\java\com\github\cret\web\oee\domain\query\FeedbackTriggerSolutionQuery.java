package com.github.cret.web.oee.domain.query;

import java.util.Date;

public class FeedbackTriggerSolutionQuery {

	private String triggerRecordId;

	private String solverId;

	private String solverName;

	private Date startSolveTime;

	private Date endSolveTime;

	private Boolean submited;

	private String solution;

	public String getTriggerRecordId() {
		return triggerRecordId;
	}

	public void setTriggerRecordId(String triggerRecordId) {
		this.triggerRecordId = triggerRecordId;
	}

	public String getSolverId() {
		return solverId;
	}

	public void setSolverId(String solverId) {
		this.solverId = solverId;
	}

	public String getSolverName() {
		return solverName;
	}

	public void setSolverName(String solverName) {
		this.solverName = solverName;
	}

	public Date getStartSolveTime() {
		return startSolveTime;
	}

	public void setStartSolveTime(Date startSolveTime) {
		this.startSolveTime = startSolveTime;
	}

	public Date getEndSolveTime() {
		return endSolveTime;
	}

	public void setEndSolveTime(Date endSolveTime) {
		this.endSolveTime = endSolveTime;
	}

	public Boolean getSubmited() {
		return submited;
	}

	public void setSubmited(Boolean submited) {
		this.submited = submited;
	}

	public String getSolution() {
		return solution;
	}

	public void setSolution(String solution) {
		this.solution = solution;
	}

}
