package com.github.cret.web.oee.document.analyze;

import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

/**
 * 目标OEE
 */
@Document(collection = "target_oee")
public class TargetOee {

	@Id
	private String id;

	// 可用性
	@Field(name = "availability")
	private Double availability;

	// 性能
	@Field(name = "performance")
	private Double performance;

	// 质量
	@Field(name = "quality")
	private Double quality;

	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	}

	public Double getAvailability() {
		return availability;
	}

	public void setAvailability(Double availability) {
		this.availability = availability;
	}

	public Double getPerformance() {
		return performance;
	}

	public void setPerformance(Double performance) {
		this.performance = performance;
	}

	public Double getQuality() {
		return quality;
	}

	public void setQuality(Double quality) {
		this.quality = quality;
	}

	public Double getOee() {
		return this.availability * this.performance * this.quality;
	}

}
