{
    "java.compile.nullAnalysis.mode": "automatic",
    "java.configuration.updateBuildConfiguration": "automatic",
    "formatFiles.excludePattern": "*.class",
    "replacerules.rules": {
        "controller": {
            "find": "(.+) (.+)\\((.+);",
            "replace": "@RequestMapping(\"/$2\")  public $1 $2 ($3{\n}",
            "languages": [
                "java"
            ]
        }
    },
    "java.jdt.ls.vmargs": "-XX:+UseParallelGC -XX:GCTimeRatio=4 -XX:AdaptiveSizePolicyWeight=90 -Dsun.zip.disableMemoryMapping=true -Xmx4G -Xms100m -Xlog:disable",

}