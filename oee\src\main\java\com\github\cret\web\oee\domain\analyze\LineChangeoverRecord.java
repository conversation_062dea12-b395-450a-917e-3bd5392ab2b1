package com.github.cret.web.oee.domain.analyze;

import java.util.Date;

/**
 * 线体换线记录
 */
public class LineChangeoverRecord {

	// 开始日期
	Date startTime;

	// 结束日期
	Date endTime;

	// 时间间隔
	Long Interval;

	public Date getStartTime() {
		return startTime;
	}

	public void setStartTime(Date startTime) {
		this.startTime = startTime;
	}

	public Date getEndTime() {
		return endTime;
	}

	public void setEndTime(Date endTime) {
		this.endTime = endTime;
	}

	public Long getInterval() {
		return Interval;
	}

	public void setInterval(Long interval) {
		Interval = interval;
	}

}