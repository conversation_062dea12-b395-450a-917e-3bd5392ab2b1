package com.github.cret.web.security.config;

import org.casbin.casdoor.entity.User;
import org.casbin.casdoor.exception.AuthException;
import org.casbin.casdoor.service.AuthService;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.stereotype.Component;

@Component
@EnableConfigurationProperties(CasdoorProperties.class)
public class CasdoorService {

	private final AuthService authService;

	// private UserService userService;
	// private TokenService tokenService;
	// private SessionService sessionService;

	public CasdoorService(CasdoorProperties properties) {
		this.authService = new AuthService(properties);
		// this.sessionService = new SessionService(properties);
		// this.userService = new UserService(properties);
		// this.tokenService = new TokenService(properties);
	}

	public User auth(String code, String state) throws AuthException {
		String token = authService.getOAuthToken(code, state);
		return authService.parseJwtToken(token);
	}

}
