package com.github.cret.web.oee.service;

import org.bson.Document;

import com.github.cret.web.common.domain.PageList;
import com.github.cret.web.common.domain.PageableParam;
import com.github.cret.web.oee.domain.log.LogRecord;
import com.github.cret.web.oee.domain.log.LogSearchParam;

public interface ProductionLogService {

	PageList<LogRecord> page(PageableParam<LogSearchParam> param);

	Document get(String deviceCode, String id);

}
