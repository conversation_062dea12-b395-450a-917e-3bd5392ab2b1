package com.github.cret.web.oee.domain.print_gkg;

import org.springframework.data.mongodb.core.mapping.Field;

public record StatusLog(
		// 条形码
		@Field("barcode") String barCode,
		// 机器名称
		@Field("machinename") String machineName,
		// 状态
		@Field("status") String status,
		// 开始时间
		@Field("starttime") String startTime,
		// 结束时间
		@Field("endtime") String endTime,
		// 描述
		@Field("describe") String describe,
		// 值
		@Field("value") String value,
		// 值1
		@Field("value1") String value1,
		// 值2
		@Field("value2") String value2,
		// 值3
		@Field("value3") String value3,
		// 值4
		@Field("value4") String value4,
		// 值5
		@Field("value5") String value5) {

}
