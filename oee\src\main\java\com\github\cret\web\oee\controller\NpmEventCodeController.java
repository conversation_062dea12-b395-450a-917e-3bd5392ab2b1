package com.github.cret.web.oee.controller;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.github.cret.web.common.domain.PageList;
import com.github.cret.web.common.domain.PageableParam;
import com.github.cret.web.oee.document.event.NpmEventCode;
import com.github.cret.web.oee.enums.EventType;
import com.github.cret.web.oee.service.NpmEventCodeService;

@RestController
@RequestMapping("/npm-event-code")
public class NpmEventCodeController {

	@Autowired
	private NpmEventCodeService service;

	@PostMapping
	public NpmEventCode create(@RequestBody NpmEventCode npmEventCode) {
		return service.save(npmEventCode);
	}

	@GetMapping
	public List<NpmEventCode> findAll() {
		return service.findAll();
	}

	@GetMapping("/{id}")
	public NpmEventCode findById(@PathVariable String id) {
		return service.findById(id);
	}

	@PostMapping("/page")
	public PageList<NpmEventCode> page(@RequestBody PageableParam<NpmEventCode> param) {
		return service.page(param);
	}

	@PutMapping("/{id}")
	public NpmEventCode update(@PathVariable String id, @RequestBody NpmEventCode npmEventCode) {
		npmEventCode.setId(id);
		return service.save(npmEventCode);
	}

	@GetMapping("/main-code/{mainCode}")
	public List<NpmEventCode> findByMainCode(@PathVariable String mainCode) {
		return service.findByMainCode(mainCode);
	}

	@GetMapping("/event-type/{eventType}")
	public List<NpmEventCode> findByEventType(@PathVariable EventType eventType) {
		return service.findByEventType(eventType);
	}

}