package com.github.cret.web.oee.controller;

import java.util.List;

import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.github.cret.web.common.annotation.OpLog;
import com.github.cret.web.common.domain.PageList;
import com.github.cret.web.common.domain.PageableParam;
import com.github.cret.web.oee.document.Device;
import com.github.cret.web.oee.domain.device.StatusResponse;
import com.github.cret.web.oee.enums.DeviceCategory;
import com.github.cret.web.oee.service.DeviceService;

@RestController
@RequestMapping("/device")
public class DeviceController {

	private final DeviceService deviceService;

	public DeviceController(DeviceService deviceService) {
		this.deviceService = deviceService;
	}

	@PostMapping
	@OpLog(name = "创建设备")
	public Device create(@RequestBody Device device) {
		return deviceService.save(device);
	}

	@GetMapping("/{id}")
	public Device getById(@PathVariable String id) {
		return deviceService.findById(id);
	}

	@PostMapping("/page")
	public PageList<Device> page(@RequestBody PageableParam<Device> param) {
		return deviceService.page(param);
	}

	@GetMapping
	public List<Device> getAll() {
		return deviceService.findAll();
	}

	@PutMapping("/{id}")
	@OpLog(name = "更新设备")
	public Device update(@PathVariable String id, @RequestBody Device device) {
		device.setId(id);
		return deviceService.save(device);
	}

	@DeleteMapping("/{id}")
	@OpLog(name = "删除设备")
	public void delete(@PathVariable String id) {
		deviceService.deleteById(id);
	}

	@DeleteMapping("/batch")
	@OpLog(name = "批量删除设备")
	public void batchDelete(@RequestBody List<String> ids) {
		deviceService.batchDelete(ids);
	}

	// 已有业务接口
	@GetMapping("/code/{code}")
	public Device getByCode(@PathVariable String code) {
		return deviceService.getByCode(code);
	}

	@GetMapping("/list/{lineCode}")
	public List<Device> getAllDevices(@PathVariable String lineCode) {
		return deviceService.getAllDevices(lineCode);
	}

	@GetMapping("/list/{lineCode}/{category}")
	public List<Device> getDevicesByCategory(@PathVariable String lineCode, @PathVariable DeviceCategory category) {
		return deviceService.getDevicesByCategory(lineCode, category);
	}

	@GetMapping("/status/{code}")
	public StatusResponse getClientStatus(@PathVariable String code) {
		return deviceService.getClientStatus(code);
	}

}