package com.github.cret.web.oee.repository;

import java.util.Date;
import java.util.List;

import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.stereotype.Repository;

import com.github.cret.web.oee.document.feedback.FeedbackTriggerSend;

@Repository
public interface FeedbackTriggerSendRepository extends MongoRepository<FeedbackTriggerSend, String> {

	/**
	 * 根据触发记录ID查找发送记录
	 * @param triggerRecordId 触发记录ID
	 * @return 发送记录列表
	 */
	List<FeedbackTriggerSend> findByTriggerRecordId(String triggerRecordId);

	/**
	 * 根据发送状态查找发送记录（向后兼容Boolean）
	 * @param sendStatus 发送状态
	 * @return 发送记录列表
	 */
	List<FeedbackTriggerSend> findBySendStatus(Boolean sendStatus);

	/**
	 * 根据发送状态查找发送记录（新的String状态）
	 * @param sendStatus 发送状态字符串
	 * @return 发送记录列表
	 */
	List<FeedbackTriggerSend> findBySendStatus(String sendStatus);

	/**
	 * 根据触发记录ID和发送状态查找发送记录（向后兼容Boolean）
	 * @param triggerRecordId 触发记录ID
	 * @param sendStatus 发送状态
	 * @return 发送记录列表
	 */
	List<FeedbackTriggerSend> findByTriggerRecordIdAndSendStatus(String triggerRecordId, Boolean sendStatus);

	/**
	 * 根据触发记录ID和发送状态查找发送记录（新的String状态）
	 * @param triggerRecordId 触发记录ID
	 * @param sendStatus 发送状态字符串
	 * @return 发送记录列表
	 */
	List<FeedbackTriggerSend> findByTriggerRecordIdAndSendStatus(String triggerRecordId, String sendStatus);

	/**
	 * 查找预期发送时间在指定时间之前且未发送的记录（向后兼容Boolean）
	 * @param expectedSendTime 预期发送时间
	 * @return 发送记录列表
	 */
	List<FeedbackTriggerSend> findByExpectedSendTimeBeforeAndSendStatusFalse(Date expectedSendTime);

	/**
	 * 查找预期发送时间在指定时间之前且状态为未发送的记录（新的String状态）
	 * @param expectedSendTime 预期发送时间
	 * @param sendStatus 发送状态字符串
	 * @return 发送记录列表
	 */
	List<FeedbackTriggerSend> findByExpectedSendTimeBeforeAndSendStatus(Date expectedSendTime, String sendStatus);

	/**
	 * 查找需要发送的记录：预期发送时间小于等于当前时间且未发送的记录
	 * @param currentTime 当前时间
	 * @return 需要发送的记录列表
	 */
	List<FeedbackTriggerSend> findByExpectedSendTimeLessThanEqualAndSendStatusIsNull(Date currentTime);

	/**
	 * 查找需要发送的记录：预期发送时间小于等于当前时间且发送状态为false的记录（向后兼容Boolean）
	 * @param currentTime 当前时间
	 * @param sendStatus 发送状态
	 * @return 需要发送的记录列表
	 */
	List<FeedbackTriggerSend> findByExpectedSendTimeLessThanEqualAndSendStatus(Date currentTime, Boolean sendStatus);

	/**
	 * 查找需要发送的记录：预期发送时间小于等于当前时间且发送状态为指定值的记录（新的String状态）
	 * @param currentTime 当前时间
	 * @param sendStatus 发送状态字符串
	 * @return 需要发送的记录列表
	 */
	List<FeedbackTriggerSend> findByExpectedSendTimeLessThanEqualAndSendStatus(Date currentTime, String sendStatus);

	/**
	 * 查找预期发送时间在指定时间范围内的记录
	 * @param startTime 开始时间
	 * @param endTime 结束时间
	 * @return 发送记录列表
	 */
	List<FeedbackTriggerSend> findByExpectedSendTimeBetween(Date startTime, Date endTime);

}
