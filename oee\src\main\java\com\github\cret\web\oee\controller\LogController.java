package com.github.cret.web.oee.controller;

import org.bson.Document;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.github.cret.web.common.domain.PageList;
import com.github.cret.web.common.domain.PageableParam;
import com.github.cret.web.oee.domain.log.LogRecord;
import com.github.cret.web.oee.domain.log.LogSearchParam;
import com.github.cret.web.oee.service.ProductionLogService;

@RestController
@RequestMapping("/log")
public class LogController {

	private final ProductionLogService productionLogService;

	public LogController(ProductionLogService productionLogService) {
		this.productionLogService = productionLogService;
	}

	@GetMapping("/production")
	public Document getProductionLog(@RequestParam String deviceCode, @RequestParam String id) {
		return productionLogService.get(deviceCode, id);
	}

	@PostMapping("/production/page")
	public PageList<LogRecord> postMethodName(@RequestBody PageableParam<LogSearchParam> param) {
		return productionLogService.page(param);
	}

}
