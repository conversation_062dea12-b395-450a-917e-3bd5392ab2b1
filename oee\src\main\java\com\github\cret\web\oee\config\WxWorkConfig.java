package com.github.cret.web.oee.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

// WxWorkConfig 是用于存储和管理企业微信相关配置的配置类。
@Component
@ConfigurationProperties(prefix = "wx-work")
public class WxWorkConfig {

	private String sendSeverUrl;

	// 发送消息的接口地址
	private String sendMsgApi;

	// 企业微信应用的 agentId
	private int agentId;

	// 是否启用ID转换
	private int enableIdTrans;

	// 是否启用重复消息检查
	private int enableDuplicateCheck;

	// 重复消息检查的时间间隔（秒）
	private int duplicateCheckInterval;

	// 消息是否为保密消息（0 表示否，1 表示是）
	private int safe;

	public String getSendSeverUrl() {
		return sendSeverUrl;
	}

	public void setSendSeverUrl(String sendSeverUrl) {
		this.sendSeverUrl = sendSeverUrl;
	}

	public String getSendMsgApi() {
		return sendMsgApi;
	}

	public void setSendMsgApi(String sendMsgApi) {
		this.sendMsgApi = sendMsgApi;
	}

	public int getAgentId() {
		return agentId;
	}

	public void setAgentId(int agentId) {
		this.agentId = agentId;
	}

	public int getEnableIdTrans() {
		return enableIdTrans;
	}

	public void setEnableIdTrans(int enableIdTrans) {
		this.enableIdTrans = enableIdTrans;
	}

	public int getEnableDuplicateCheck() {
		return enableDuplicateCheck;
	}

	public void setEnableDuplicateCheck(int enableDuplicateCheck) {
		this.enableDuplicateCheck = enableDuplicateCheck;
	}

	public int getDuplicateCheckInterval() {
		return duplicateCheckInterval;
	}

	public void setDuplicateCheckInterval(int duplicateCheckInterval) {
		this.duplicateCheckInterval = duplicateCheckInterval;
	}

	public int getSafe() {
		return safe;
	}

	public void setSafe(int safe) {
		this.safe = safe;
	}

}
