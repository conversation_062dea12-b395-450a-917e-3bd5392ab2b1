package com.github.cret.web.oee.service;

import com.github.cret.web.common.domain.PageableParam;
import com.github.cret.web.oee.document.TDeviceConfig;
import com.github.cret.web.common.domain.PageList;

public interface TDeviceConfigService {

	PageList<TDeviceConfig> page(PageableParam<String> param);

	void save(TDeviceConfig tDeviceConfig);

	void deleteById(String id);

	TDeviceConfig findById(String id);

}
