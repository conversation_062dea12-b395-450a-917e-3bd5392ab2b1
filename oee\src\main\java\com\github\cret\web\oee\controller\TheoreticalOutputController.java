package com.github.cret.web.oee.controller;

import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import com.alibaba.excel.EasyExcel;
import com.github.cret.web.common.domain.PageList;
import com.github.cret.web.common.domain.PageableParam;
import com.github.cret.web.oee.document.analyze.TheoreticalOutput;
import com.github.cret.web.oee.service.TheoreticalOutputService;

import jakarta.servlet.http.HttpServletResponse;

@RestController
@RequestMapping("/theoretical-output")
public class TheoreticalOutputController {

	@Autowired
	private TheoreticalOutputService theoreticalOutputService;

	@PostMapping
	public TheoreticalOutput create(@RequestBody TheoreticalOutput theoreticalOutput) {
		return theoreticalOutputService.save(theoreticalOutput);
	}

	@GetMapping("/{id}")
	public TheoreticalOutput getById(@PathVariable String id) {
		return theoreticalOutputService.findById(id);
	}

	@PostMapping("/page")
	public PageList<TheoreticalOutput> page(@RequestBody PageableParam<TheoreticalOutput> param) {
		return theoreticalOutputService.page(param);
	}

	@GetMapping
	public List<TheoreticalOutput> getAll() {
		return theoreticalOutputService.findAll();
	}

	@PutMapping("/{id}")
	public TheoreticalOutput update(@PathVariable String id, @RequestBody TheoreticalOutput theoreticalOutput) {
		theoreticalOutput.setId(id);
		return theoreticalOutputService.save(theoreticalOutput);
	}

	@DeleteMapping("/{id}")
	public void delete(@PathVariable String id) {
		theoreticalOutputService.deleteById(id);
	}

	@DeleteMapping("/batch")
	public void batchDelete(@RequestBody List<String> ids) {
		theoreticalOutputService.batchDelete(ids);
	}

	@PostMapping("/import")
	public void importExcel(@RequestParam("file") MultipartFile file) throws IOException {
		theoreticalOutputService.importExcel(file);
	}

	@PostMapping("/export")
	public void exportExcel(HttpServletResponse response, @RequestBody TheoreticalOutput param) throws IOException {
		response.setContentType("application/vnd.ms-excel");
		response.setCharacterEncoding("utf-8");
		String fileName = URLEncoder.encode("理论产出数据", StandardCharsets.UTF_8);
		response.setHeader("Content-disposition", "attachment;filename=" + fileName + ".xlsx");

		List<TheoreticalOutput> dataList = theoreticalOutputService.findList(param);
		EasyExcel.write(response.getOutputStream(), TheoreticalOutput.class).sheet("理论产出").doWrite(dataList);
	}

}