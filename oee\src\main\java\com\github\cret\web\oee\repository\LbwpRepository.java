package com.github.cret.web.oee.repository;

import java.util.List;

import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.stereotype.Repository;

import com.github.cret.web.oee.document.mes.LabelLatestStatus;

@Repository
public interface LbwpRepository extends MongoRepository<LabelLatestStatus, String> {

	List<LabelLatestStatus> findAllByLbIdIn(List<String> aListOfLbIds);

}
