package com.github.cret.web.security.config;

import org.springframework.boot.context.properties.ConfigurationProperties;

@ConfigurationProperties(prefix = "security.jwt")
public class JwtProperties {

	private String publicKey;

	private String privateKey;

	private Integer accessTokenExpires;

	private Integer refreshTokenExpires;

	public Integer getAccessTokenExpires() {
		return accessTokenExpires;
	}

	public void setAccessTokenExpires(Integer accessTokenExpires) {
		this.accessTokenExpires = accessTokenExpires;
	}

	public Integer getRefreshTokenExpires() {
		return refreshTokenExpires;
	}

	public void setRefreshTokenExpires(Integer refreshTokenExpires) {
		this.refreshTokenExpires = refreshTokenExpires;
	}

	public String getPublicKey() {
		return publicKey;
	}

	public void setPublicKey(String publicKey) {
		this.publicKey = publicKey;
	}

	public String getPrivateKey() {
		return privateKey;
	}

	public void setPrivateKey(String privateKey) {
		this.privateKey = privateKey;
	}

}
