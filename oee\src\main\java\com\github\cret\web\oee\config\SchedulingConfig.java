package com.github.cret.web.oee.config;

import java.util.concurrent.Executors;

import org.springframework.context.annotation.Configuration;
import org.springframework.lang.NonNull;
import org.springframework.scheduling.annotation.SchedulingConfigurer;
import org.springframework.scheduling.config.ScheduledTaskRegistrar;

@Configuration
public class SchedulingConfig implements SchedulingConfigurer {

	@Override
	public void configureTasks(@NonNull ScheduledTaskRegistrar taskRegistrar) {
		// 设置一个大小为10的线程池来执行所有定时任务
		taskRegistrar.setScheduler(Executors.newScheduledThreadPool(10));
	}

}