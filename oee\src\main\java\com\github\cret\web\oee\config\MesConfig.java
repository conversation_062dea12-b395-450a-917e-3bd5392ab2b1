package com.github.cret.web.oee.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * MES服务配置类，用于管理MES相关的URL配置
 */
@Component
@ConfigurationProperties(prefix = "mes")
public class MesConfig {

	/**
	 * 标签工艺过程服务URL
	 */
	private String labelWorkProcessUrl = "http://localhost:9996/oee-mes-server/label-work-process";

	/**
	 * 样品服务URL
	 */
	private String sampleUrl = "http://localhost:9996/oee-mes-server/sample";

	/**
	 * 标签工艺过程分析服务URL
	 */
	private String labelWorkProcessAnalysisUrl = "http://localhost:9996/oee-mes-server/oee";

	public String getLabelWorkProcessUrl() {
		return labelWorkProcessUrl;
	}

	public void setLabelWorkProcessUrl(String labelWorkProcessUrl) {
		this.labelWorkProcessUrl = labelWorkProcessUrl;
	}

	public String getSampleUrl() {
		return sampleUrl;
	}

	public void setSampleUrl(String sampleUrl) {
		this.sampleUrl = sampleUrl;
	}

	public String getLabelWorkProcessAnalysisUrl() {
		return labelWorkProcessAnalysisUrl;
	}

	public void setLabelWorkProcessAnalysisUrl(String labelWorkProcessAnalysisUrl) {
		this.labelWorkProcessAnalysisUrl = labelWorkProcessAnalysisUrl;
	}

}
