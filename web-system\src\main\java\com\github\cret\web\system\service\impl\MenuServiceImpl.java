package com.github.cret.web.system.service.impl;

import java.util.List;
import java.util.Set;

import org.springframework.data.domain.Example;
import org.springframework.data.domain.ExampleMatcher;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;

import com.github.cret.web.common.domain.PageList;
import com.github.cret.web.common.domain.PageableParam;
import com.github.cret.web.security.domain.AuthUser;
import com.github.cret.web.security.util.SecurityUtil;
import com.github.cret.web.system.document.MenuItem;
import com.github.cret.web.system.domain.MenuItemCreate;
import com.github.cret.web.system.mapper.MenuItemMapper;
import com.github.cret.web.system.repository.MenuItemRepository;
import com.github.cret.web.system.service.MenuService;

@Service
public class MenuServiceImpl implements MenuService {

	private final MongoTemplate mongoTemplate;

	private final MenuItemRepository repository;

	private final MenuItemMapper mapper;

	public MenuServiceImpl(MongoTemplate mongoTemplate, MenuItemMapper mapper, MenuItemRepository repository) {
		this.mongoTemplate = mongoTemplate;
		this.repository = repository;
		this.mapper = mapper;
	}

	@Override
	public List<MenuItem> getUserMenu() {
		AuthUser authUser = SecurityUtil.getCurrentUser();
		assert authUser != null;
		if (authUser.isAdmin()) {
			return repository.findAll(Sort.by(Sort.Direction.ASC, "order"));
		}
		else {
			Set<String> perms = authUser.perms();
			return mongoTemplate
				.find(Query
					.query(new Criteria().orOperator(Criteria.where("access").in(perms),
							Criteria.where("access").isNull(), Criteria.where("access").is("")))
					.with(Sort.by(Sort.Direction.ASC, "order")), MenuItem.class);
		}

	}

	@Override
	public List<MenuItem> listItem(MenuItem param) {
		return repository.findAll(Example.of(param));
	}

	@Override
	public List<MenuItem> listChildren(String parentId) {
		return repository.findAllByParentId(parentId);
	}

	@Override
	public List<MenuItem> listRootItem() {
		return mongoTemplate.find(Query.query(Criteria.where("parentId").isNull()), MenuItem.class);
	}

	@Override
	public PageList<MenuItem> pageItem(PageableParam<MenuItem> param) {
		ExampleMatcher matcher = ExampleMatcher.matching()
			.withIgnoreNullValues()
			.withMatcher("title", ExampleMatcher.GenericPropertyMatchers.contains())
			.withMatcher("path", ExampleMatcher.GenericPropertyMatchers.startsWith());
		Example<MenuItem> example = Example.of(param.getSearchParams(), matcher);

		// 设置默认排序字段
		if (param.getPageData().getSortField() == null) {
			param.getPageData().setSortField("order");
		}

		return PageList.fromPage(repository.findAll(example, param.getPageRequest()));
	}

	@Override
	public void delete(String id) {
		repository.deleteById(id);
	}

	@Override
	public void update(String id, MenuItem menuItem) {
		menuItem.setId(id);
		repository.save(menuItem);
	}

	@Override
	public MenuItem get(String id) {
		return repository.findById(id).get();
	}

	@Override
	public void create(MenuItemCreate param) {
		repository.save(mapper.toEntity(param));
	}

}
