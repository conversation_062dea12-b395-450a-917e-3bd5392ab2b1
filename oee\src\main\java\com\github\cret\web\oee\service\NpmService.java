package com.github.cret.web.oee.service;

import java.util.List;

import com.github.cret.web.oee.domain.npm.LoginRes;
import com.github.cret.web.oee.domain.npm.ReportRes;

import reactor.core.publisher.Mono;

public interface NpmService {

	public Mono<LoginRes> getLogin();

	public Mono<List<ReportRes>> getReferProductManageReportAction();

	public Mono<String> getReferProductManageReportDtlAction(String listindex);

}
