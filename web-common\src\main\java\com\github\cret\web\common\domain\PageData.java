package com.github.cret.web.common.domain;

import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;

public class PageData {

	private Integer pageNumber;

	private Integer pageSize;

	private String sortField;

	private String sortDirection;

	public PageData() {
		pageNumber = 0;
		pageSize = 10;
		sortDirection = "ASC";
	}

	public PageRequest toPageRequest() {
		if (sortField != null && !sortField.isEmpty()) {
			Sort.Direction direction = Sort.Direction.fromString(sortDirection);
			return PageRequest.of(pageNumber, pageSize, Sort.by(direction, sortField));
		}
		return PageRequest.of(pageNumber, pageSize);
	}

	public Integer getPageNumber() {
		return pageNumber;
	}

	public void setPageNumber(Integer pageNumber) {
		this.pageNumber = pageNumber;
	}

	public Integer getPageSize() {
		return pageSize;
	}

	public void setPageSize(Integer pageSize) {
		this.pageSize = pageSize;
	}

	public String getSortField() {
		return sortField;
	}

	public void setSortField(String sortField) {
		this.sortField = sortField;
	}

	public String getSortDirection() {
		return sortDirection;
	}

	public void setSortDirection(String sortDirection) {
		this.sortDirection = sortDirection;
	}

}
