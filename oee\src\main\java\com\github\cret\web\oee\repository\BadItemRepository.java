package com.github.cret.web.oee.repository;

import java.util.List;
import java.util.Optional;

import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.stereotype.Repository;

import com.github.cret.web.oee.document.BadItemDoc;

@Repository
public interface BadItemRepository extends MongoRepository<BadItemDoc, String> {

	/**
	 * 根据不良项目ID查找不良项目信息
	 * @param badItemId 不良项目ID
	 * @return 不良项目信息
	 */
	Optional<BadItemDoc> findByBadItemId(String badItemId);

	/**
	 * 根据不良项目ID列表查找不良项目信息
	 * @param badItemIds 不良项目ID列表
	 * @return 不良项目信息列表
	 */
	List<BadItemDoc> findByBadItemIdIn(List<String> badItemIds);

	/**
	 * 根据不良类型ID查找不良项目信息
	 * @param badTypeId 不良类型ID
	 * @return 不良项目信息列表
	 */
	List<BadItemDoc> findByBadTypeId(String badTypeId);

	/**
	 * 根据不良项目名称查找不良项目信息
	 * @param badItemName 不良项目名称
	 * @return 不良项目信息
	 */
	Optional<BadItemDoc> findByBadItemName(String badItemName);

	/**
	 * 根据不良项目名称模糊查询
	 * @param badItemName 不良项目名称关键字
	 * @return 不良项目信息列表
	 */
	List<BadItemDoc> findByBadItemNameContaining(String badItemName);

}
