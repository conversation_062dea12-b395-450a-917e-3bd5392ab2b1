package com.github.cret.web.oee.service.impl;

import java.io.IOException;
import java.util.List;

import org.springframework.data.domain.Example;
import org.springframework.data.domain.ExampleMatcher;
import org.springframework.data.domain.ExampleMatcher.GenericPropertyMatchers;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import com.alibaba.excel.EasyExcel;
import com.github.cret.web.common.domain.PageList;
import com.github.cret.web.common.domain.PageableParam;
import com.github.cret.web.oee.document.analyze.Product;
import com.github.cret.web.oee.excel.ProductListener;
import com.github.cret.web.oee.repository.ProductRepository;
import com.github.cret.web.oee.service.ProductService;

@Service
public class ProductServiceImpl implements ProductService {

	private final ProductRepository repository;

	private final MongoTemplate mongoTemplate;

	public ProductServiceImpl(ProductRepository repository, MongoTemplate mongoTemplate) {
		this.repository = repository;
		this.mongoTemplate = mongoTemplate;
	}

	@Override
	public Product save(Product product) {
		return mongoTemplate.save(product);
	}

	@Override
	public PageList<Product> page(PageableParam<Product> param) {
		ExampleMatcher matcher = ExampleMatcher.matching()
			.withIgnoreNullValues()
			.withMatcher("lineCode", GenericPropertyMatchers.contains())
			.withMatcher("productModel", GenericPropertyMatchers.contains());
		Example<Product> example = Example.of(param.getSearchParams(), matcher);
		return PageList.fromPage(repository.findAll(example, param.getPageRequest()));
	}

	@Override
	public List<Product> findList(Product param) {
		return repository.findAll(Example.of(param));
	}

	@Override
	public Product findById(String id) {
		return mongoTemplate.findById(id, Product.class);
	}

	@Override
	public List<Product> findAll() {
		return mongoTemplate.findAll(Product.class);
	}

	@Override
	public void deleteById(String id) {
		Product product = findById(id);
		if (product != null) {
			mongoTemplate.remove(product);
		}
	}

	@Override
	public void importExcel(MultipartFile file) throws IOException {
		EasyExcel.read(file.getInputStream(), Product.class, new ProductListener(repository)).sheet().doRead();
	}

	@Override
	public Product findOneByProductModel(String productModel) {
		return repository.findOneByProductModel(productModel);
	}

}
