package com.github.cret.web.common.config;

import org.springframework.boot.web.servlet.error.DefaultErrorAttributes;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.format.FormatterRegistry;
import org.springframework.format.datetime.DateFormatter;
import org.springframework.format.datetime.standard.DateTimeFormatterRegistrar;
import org.springframework.lang.NonNull;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import com.github.cret.web.common.component.CustomDefaultErrorAttributes;

@Configuration
public class WebConfig implements WebMvcConfigurer {

	@Bean
	public DefaultErrorAttributes errorAttributes() {
		return new CustomDefaultErrorAttributes();
	}

	@Override
	public void addFormatters(@NonNull FormatterRegistry registry) {

		DateTimeFormatterRegistrar registrar = new DateTimeFormatterRegistrar();
		registrar.setUseIsoFormat(true); // 设置为ISO标准格式
		{
			registry.addFormatter(new DateFormatter("yyyy-MM-dd'T'HH:mm:ss.SSSXXX"));
			registrar.registerFormatters(registry);
		}
	}

}
