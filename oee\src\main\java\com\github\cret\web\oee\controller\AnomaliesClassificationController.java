package com.github.cret.web.oee.controller;

import com.github.cret.web.common.domain.PageList;
import com.github.cret.web.common.domain.PageableParam;
import com.github.cret.web.oee.document.feedback.AnomaliesClassification;
import com.github.cret.web.oee.domain.anomalies.AnomaliesClassificationSearch;
import com.github.cret.web.oee.service.AnomaliesClassificationService;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/anomaliesClassification")
public class AnomaliesClassificationController {

	@Resource
	private AnomaliesClassificationService anomaliesClassificationService;

	/**
	 * 获取列表(分页)
	 */
	@PostMapping("/page")
	public PageList<AnomaliesClassification> page(@RequestBody PageableParam<AnomaliesClassificationSearch> param) {
		return anomaliesClassificationService.page(param);
	}

	/**
	 * 获取
	 */
	@GetMapping("/{id}")
	public AnomaliesClassification get(@PathVariable String id) {
		return anomaliesClassificationService.findById(id);
	}

	/**
	 * 添加
	 */
	@PostMapping("/add")
	public void add(@RequestBody AnomaliesClassification anomaliesClassification) {
		anomaliesClassificationService.save(anomaliesClassification);
	}

	/**
	 * 修改
	 */
	@PutMapping("/{id}")
	public void update(@PathVariable String id, @RequestBody AnomaliesClassification anomaliesClassification) {
		anomaliesClassification.setId(id);
		anomaliesClassificationService.save(anomaliesClassification);
	}

	/**
	 * 删除
	 */
	@DeleteMapping("/{id}")
	public void delete(@PathVariable String id) {
		anomaliesClassificationService.deleteById(id);
	}

	/**
	 * 更新启用状态
	 */
	@PutMapping("/{id}/enable")
	public void updateEnable(@PathVariable String id, @RequestParam Boolean enable) {
		anomaliesClassificationService.updateEnable(id, enable);
	}

	/**
	 * 根据线体编码和异常编码查找唯一的异常配置
	 */
	@GetMapping("/findByLineCodeAndAnomaliesCode")
	public AnomaliesClassification findByLineCodeAndAnomaliesCode(@RequestParam("lineCode") String lineCode,
			@RequestParam("anomaliesCode") String anomaliesCode) {
		return anomaliesClassificationService.findByLineCodeAndAnomaliesCode(lineCode, anomaliesCode);
	}

}
