package com.github.cret.web.oee.utils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * oee指标计算工具类
 */
public class OeeUtil {

	/**
	 * 获取生产日志集合
	 * @param deviceCode
	 * @return
	 */
	public static String getProductionLogCollection(String deviceCode) {
		return deviceCode + "_PRODUCTION";
	}

	/**
	 * 获取CT日志集合
	 * @param deviceCode
	 * @return
	 */
	public static String getCTLogCollection(String deviceCode) {
		return deviceCode + "_PCBLOG";
	}

	/**
	 * 生产对应名称的候选键
	 * @param productModel
	 * @return
	 */
	public static List<String> generateCandidateKeys(String productModel) {
		List<String> candidates = new ArrayList<>();
		String[] parts = productModel.split("-");

		// 逆向扫描寻找最后一个BOT/TOP
		int keywordPos = -1;
		String keyword = null;
		for (int i = parts.length - 1; i >= 0; i--) {
			if ("BOT".equalsIgnoreCase(parts[i])) {
				keywordPos = i;
				keyword = "BOT";
				break;
			}
			else if ("TOP".equalsIgnoreCase(parts[i])) {
				keywordPos = i;
				keyword = "TOP";
				break;
			}
		}

		candidates.add(productModel);

		if (keywordPos == -1) {
			return candidates;
		}

		// 构建基础前缀（不包含关键词）
		String prefix = String.join("-", Arrays.copyOfRange(parts, 0, keywordPos)) + "-";

		// 根据关键词生成候选
		if ("BOT".equals(keyword)) {
			candidates.add(prefix + "B");
			for (int i = 1; i <= 3; i++) {
				candidates.add(prefix + "B" + i);
			}
		}
		else if ("TOP".equals(keyword)) {
			candidates.add(prefix + "T");
			for (int i = 1; i <= 3; i++) {
				candidates.add(prefix + "T" + i);
			}
		}

		return candidates;
	}

}
