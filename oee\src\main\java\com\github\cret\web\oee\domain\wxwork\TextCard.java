package com.github.cret.web.oee.domain.wxwork;

import com.github.cret.web.oee.config.WxWorkConfig;

// TextCard 类用于封装企业微信文本卡片消息的请求参数。
public class TextCard extends BaseReq {

	// 文本卡片内容对象
	private TextCardContent textcard;

	// 是否开启ID转译，0表示否，1表示是
	private int enable_id_trans;

	public TextCard() {
		super();
	}

	public TextCard(String toUser, String toParty, String toTag, String msgType, TextCardContent content,
			int enable_id_trans, WxWorkConfig wxWorkConfig) {
		super(toUser, toParty, toTag, msgType, wxWorkConfig);
		this.textcard = content;
		this.enable_id_trans = enable_id_trans;
	}

	public TextCardContent getTextcard() {
		return textcard;
	}

	public void setTextcard(TextCardContent textcard) {
		this.textcard = textcard;
	}

	public int getEnable_id_trans() {
		return enable_id_trans;
	}

	public void setEnable_id_trans(int enable_id_trans) {
		this.enable_id_trans = enable_id_trans;
	}

}
