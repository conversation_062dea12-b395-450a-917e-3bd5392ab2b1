package com.github.cret.web.oee.repository;

import java.util.Date;
import java.util.List;

import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.stereotype.Repository;

import com.github.cret.web.oee.document.analyze.Abnormal;

@Repository
public interface AbnormalRepository extends MongoRepository<Abnormal, String> {

	// 查找所有 writeTime 超过指定时间且 confirmtime 为空的记录
	List<Abnormal> findByWriteTimeBeforeAndConfirmTimeIsNull(Date writeTime);

}
