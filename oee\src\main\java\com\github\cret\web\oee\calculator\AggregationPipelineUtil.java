package com.github.cret.web.oee.calculator;

import java.util.Arrays;
import java.util.Date;

import org.bson.Document;

public class AggregationPipelineUtil {

	/**
	 * 通用匹配时间范围方法
	 * @param startTime
	 * @param endTime
	 * @param timeField
	 * @return
	 */
	public static Document buildTimeRangeMatch(Date startTime, Date endTime, String timeField) {
		return new Document("$match", new Document(timeField, new Document("$gte", startTime).append("$lte", endTime)));
	}

	/**
	 * 构建产品型号过滤条件
	 * @param productModel 产品型号值（可空）
	 * @return 当productModel非空时返回匹配文档，否则返回null
	 */
	public static Document buildProductModelFilter(String productModel) {
		return buildProductModelFilter(productModel, "productmodel");
	}

	/**
	 * 构建带自定义字段名的产品过滤条件
	 * @param productModel 产品型号值（可空）
	 * @param fieldName 数据库中的字段名（默认productmodel）
	 * @return 当productModel非空时返回匹配文档，否则返回null
	 */
	public static Document buildProductModelFilter(String productModel, String fieldName) {
		if (productModel == null || productModel.trim().isEmpty()) {
			return null;
		}
		return new Document("$match", new Document(fieldName, productModel));
	}

	/**
	 * 通用排序方法
	 * @param sortField
	 * @param sortOrder
	 * @return
	 */
	public static Document buildSortStage(String sortField, int sortOrder) {
		return new Document("$sort", new Document(sortField, sortOrder));
	}

	/**
	 * 通用前值获取方法
	 * @param targetField
	 * @param outputField
	 * @param shiftBy
	 * @param defaultValue
	 * @return
	 */
	public static Document buildPreviousValueStage(String targetField, String outputField, int shiftBy,
			Object defaultValue, Document sortBy // 新增排序参数，支持多字段
	) {
		return new Document("$setWindowFields", new Document("partitionBy", null).append("sortBy", sortBy) // 使用传入的排序参数
			.append("output", new Document(outputField, new Document("$shift",
					new Document("output", "$" + targetField).append("by", shiftBy).append("default", defaultValue)))));
	}

	/**
	 * 通用变化分组ID生成方法
	 * @param checkField
	 * @param outputField
	 * @param sortSpec
	 * @return
	 */
	public static Document buildChangeGroupIdStage(String checkField, // 需要检测变化的字段名
			String compareField, // 需要比较的字段名
			String outputField, // 输出字段名
			Document sortSpec // 排序规则
	) {
		return new Document("$setWindowFields",
				new Document("partitionBy", null).append("sortBy", sortSpec)
					.append("output",
							new Document(outputField, new Document("$sum", new Document("$cond", Arrays.asList(
									new Document("$ne", Arrays.asList("$" + checkField, "$" + compareField)), 1, 0)))
								.append("window", new Document("documents", Arrays.asList("unbounded", "current"))))));
	}

}
