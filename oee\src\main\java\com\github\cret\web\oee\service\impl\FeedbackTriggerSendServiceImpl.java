package com.github.cret.web.oee.service.impl;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.function.Function;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Example;
import org.springframework.data.domain.ExampleMatcher;
import org.springframework.data.domain.ExampleMatcher.GenericPropertyMatchers;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import org.springframework.web.client.RestTemplate;

import com.github.cret.web.common.domain.PageList;
import com.github.cret.web.common.domain.PageableParam;
import com.github.cret.web.common.enumerate.SysErrEnum;
import com.github.cret.web.common.exception.SystemException;
import com.github.cret.web.common.util.JacksonUtil;
import com.github.cret.web.oee.config.FeedbackConfig;
import com.github.cret.web.oee.config.WxWorkConfig;
import com.github.cret.web.oee.document.feedback.FeedbackTriggerRecord;
import com.github.cret.web.oee.document.feedback.FeedbackTriggerSend;
import com.github.cret.web.oee.document.feedback.Reporter;
import com.github.cret.web.oee.document.feedback.Responder;
import com.github.cret.web.oee.document.feedback.ResponseConfig;
import com.github.cret.web.oee.document.feedback.SendStatus;
import com.github.cret.web.oee.document.feedback.SendUser;
import com.github.cret.web.oee.domain.request.FeedbackUrlParams;
import com.github.cret.web.oee.domain.wxwork.MsgRes;
import com.github.cret.web.oee.domain.wxwork.R;
import com.github.cret.web.oee.domain.wxwork.TextCard;
import com.github.cret.web.oee.domain.wxwork.TextCardContent;
import com.github.cret.web.oee.repository.FeedbackTriggerRecordRepository;
import com.github.cret.web.oee.repository.FeedbackTriggerSendRepository;
import com.github.cret.web.oee.service.FeedbackTriggerSendService;
import com.github.cret.web.oee.utils.BuilderUtil;
import com.github.cret.web.oee.utils.WxWorkMessageUtil;

/**
 * 反馈触发发送服务实现
 */
@Service
public class FeedbackTriggerSendServiceImpl implements FeedbackTriggerSendService {

	private static final Logger logger = LoggerFactory.getLogger(FeedbackTriggerSendServiceImpl.class);

	// 定义常量
	private static final String USER_TYPE_RESPONDER = "responder";

	private static final String ACTION_EDIT = "edit";

	private static final String ACTION_VIEW = "view";

	private static final String BUTTON_TEXT_SOLVE = "解决异常";

	private static final String BUTTON_TEXT_VIEW = "查看异常";

	private static final String MESSAGE_TITLE = "产线异常通知";

	private static final String MESSAGE_TYPE_TEXTCARD = "textcard";

	private final FeedbackTriggerSendRepository repository;

	private final FeedbackTriggerRecordRepository feedbackTriggerRecordRepository;

	private final WxWorkConfig wxWorkConfig;

	private final FeedbackConfig feedbackConfig;

	private final RestTemplate restTemplate;

	private final MongoTemplate mongoTemplate;

	public FeedbackTriggerSendServiceImpl(FeedbackTriggerSendRepository repository, WxWorkConfig wxWorkConfig,
			FeedbackConfig feedbackConfig, RestTemplate restTemplate, MongoTemplate mongoTemplate,
			FeedbackTriggerRecordRepository feedbackTriggerRecordRepository) {
		this.repository = repository;
		this.wxWorkConfig = wxWorkConfig;
		this.feedbackConfig = feedbackConfig;
		this.restTemplate = restTemplate;
		this.mongoTemplate = mongoTemplate;
		this.feedbackTriggerRecordRepository = feedbackTriggerRecordRepository;
	}

	@Override
	public void processScheduledSends() {
		Date currentTime = new Date();

		// 查找所有到期且未发送的记录
		List<FeedbackTriggerSend> pendingSends = repository
			.findByExpectedSendTimeLessThanEqualAndSendStatus(currentTime, SendStatus.UNSENT.getCode());

		if (pendingSends.isEmpty()) {
			logger.debug("没有需要发送的反馈触发记录");
			return;
		}

		logger.info("开始处理定时发送任务，共找到 {} 条待发送记录", pendingSends.size());

		// 计算1分钟前的时间点
		long oneMinuteInMillis = 60 * 1000L;
		Date oneMinuteAgo = new Date(currentTime.getTime() - oneMinuteInMillis);

		int successCount = 0;
		int failureCount = 0;
		int expiredCount = 0;

		for (FeedbackTriggerSend sendRecord : pendingSends) {
			try {
				Date expectedSendTime = sendRecord.getExpectedSendTime();

				// 判断是否超期：预期发送时间在1分钟前
				if (expectedSendTime.before(oneMinuteAgo)) {
					// 超期记录，标记为超期未发送
					sendRecord.setSendStatus(SendStatus.STOPPED);
					sendRecord.setSendTime(currentTime);
					sendRecord.setSendResult("超期未发送：预期发送时间超过1分钟");
					repository.save(sendRecord);

					expiredCount++;
					logger.warn("记录超期未发送，记录ID: {}, 预期发送时间: {}, 当前时间: {}", sendRecord.getId(), expectedSendTime,
							currentTime);
					continue;
				}

				// 在1分钟内的记录，执行发送
				String sendResult = performSend(sendRecord);

				// 更新发送状态
				sendRecord.setSendStatus(SendStatus.SENT);
				sendRecord.setSendTime(currentTime);
				sendRecord.setSendResult(sendResult);

				// 保存更新后的记录
				repository.save(sendRecord);

				successCount++;
				logger.debug("发送成功，记录ID: {}, 结果: {}", sendRecord.getId(), sendResult);

			}
			catch (Exception e) {
				// 发送失败，记录错误信息但不影响其他记录的处理
				sendRecord.setSendStatus(SendStatus.STOPPED); // 标记为已处理，避免重复发送
				sendRecord.setSendTime(currentTime);
				sendRecord.setSendResult("发送失败: " + e.getMessage());

				repository.save(sendRecord);

				failureCount++;
				logger.error("发送失败，记录ID: {}, 错误: {}", sendRecord.getId(), e.getMessage(), e);
			}
		}

		logger.info("定时发送任务完成，成功: {} 条，失败: {} 条，超期: {} 条", successCount, failureCount, expiredCount);
	}

	@Override
	public String send(FeedbackTriggerSend sendRecord) {
		Date currentTime = new Date();

		// 执行发送操作
		String sendResult = performSend(sendRecord);

		// 更新发送状态
		sendRecord.setSendStatus(SendStatus.SENT);
		sendRecord.setSendTime(currentTime);
		sendRecord.setSendResult(sendResult);

		// 保存更新后的记录
		repository.save(sendRecord);

		logger.debug("发送成功，记录ID: {}, 结果: {}", sendRecord.getId(), sendResult);
		return sendResult;
	}

	@Override
	public String performSend(FeedbackTriggerSend sendRecord) {
		List<SendUser> sendUsers = sendRecord.getSendUser();
		String triggerRecordId = sendRecord.getTriggerRecordId();
		String sendRecordId = sendRecord.getId();

		if (sendUsers == null || sendUsers.isEmpty()) {
			logger.warn("发送记录ID: {} 没有指定发送用户，跳过发送。", sendRecordId);
			return "没有指定发送用户";
		}

		StringBuilder resultBuilder = new StringBuilder(); // 用于收集所有用户的发送结果

		for (SendUser sendUser : sendUsers) {
			String sendUserId = sendUser.getSendUserId();
			String sendUserType = sendUser.getSendUserType();

			// 校验用户ID是否有效
			if (!StringUtils.hasText(sendUserId)) {
				logger.warn("发送记录ID: {} 发现无效用户ID，跳过该用户。", sendRecordId);
				resultBuilder.append(String.format("无效用户ID跳过; "));
				continue;
			}

			// 根据用户类型确定action和buttonText
			try {
				// 获取触发记录
				FeedbackTriggerRecord triggerRecord = feedbackTriggerRecordRepository.findById(triggerRecordId)
					.orElseThrow(() -> new IllegalArgumentException(
							String.format("未找到对应的触发记录，triggerRecordId: %s", triggerRecordId)));

				// 构建个性化URL
				String personalizedUrl = buildPersonalizedUrl(sendRecord, sendUser, triggerRecordId, sendRecordId);

				// 构建发送信息
				String sendInfo = buildSendInfo(triggerRecord, sendRecord.getResponseConfig());

				// 构建文本卡片
				TextCard textCard = buildTextCard(sendUser, personalizedUrl, sendInfo, sendUserType);

				// 序列化并发送消息
				String msg = JacksonUtil.tryParse(() -> JacksonUtil.getObjectMapper().writeValueAsString(textCard));
				if (msg == null) {
					logger.error("消息序列化失败，用户ID: {}, 记录ID: {}", sendUserId, sendRecordId);
					resultBuilder.append(String.format("用户 %s 消息序列化失败; ", sendUserId));
					continue; // 继续处理下一个用户
				}

				logger.info("开始向用户 {} (类型: {}) 发送消息，记录ID: {}", sendUserId, sendUserType, sendRecordId);
				R<MsgRes> sendResult = WxWorkMessageUtil.sendWxWorkMsg(msg, wxWorkConfig, restTemplate);
				String currentSendResult = WxWorkMessageUtil.buildResultText(sendResult, 1);
				resultBuilder.append(String.format("用户 %s 发送结果: %s; ", sendUserId, currentSendResult));
				logger.info("用户 {} (类型: {}) 消息发送完成，结果: {}", sendUserId, sendUserType, currentSendResult);

			}
			catch (Exception e) {
				logger.error("向用户 {} (类型: {}) 发送消息失败，记录ID: {}，错误: {}", sendUserId, sendUserType, sendRecordId,
						e.getMessage(), e);
				resultBuilder.append(String.format("用户 %s 发送失败: %s; ", sendUserId, e.getMessage()));
			}
		}

		if (resultBuilder.length() == 0) {
			return "没有用户需要发送消息或所有发送尝试失败";
		}
		return resultBuilder.toString().trim();
	}

	// ==================== 创建发送记录方法 ====================

	/**
	 * 创建反馈触发发送记录（为每个用户创建单独记录）
	 * @param triggerRecord 触发记录
	 * @param responseConfig 响应配置
	 */
	public void createFeedbackTriggerSend(FeedbackTriggerRecord triggerRecord, ResponseConfig responseConfig) {
		try {
			// 计算预期发送时间
			Date expectedSendTime = triggerRecord.getTriggerTime();
			if (responseConfig.getPointTime() != null && responseConfig.getPointTime() > 0) {
				// 将pointTime（分钟）转换为毫秒并加到触发时间上
				long pointTimeMillis = responseConfig.getPointTime() * 60 * 1000L;
				expectedSendTime = new Date(triggerRecord.getTriggerTime().getTime() + pointTimeMillis);
			}

			// 为每个响应人创建单独的发送记录
			processUserList(responseConfig.getResponders(), "responder", triggerRecord.getId(), expectedSendTime,
					responseConfig, Responder::getRespondentId, Responder::getRespondentName);

			// 为每个告知人创建单独的发送记录
			processUserList(responseConfig.getReporters(), "reporter", triggerRecord.getId(), expectedSendTime,
					responseConfig, Reporter::getReporterId, Reporter::getReportName);

		}
		catch (Exception e) {
			logger.error("创建反馈触发发送记录时发生异常，触发记录ID: {}, 错误: {}", triggerRecord.getId(), e.getMessage(), e);
			// 记录发送失败的情况，但不影响主流程
			createFailedSendRecord(triggerRecord, responseConfig, e);
		}
	}

	/**
	 * 处理用户列表并为每个有效用户创建发送记录
	 * @param <T> 用户类型（Responder 或 Reporter）
	 * @param userList 用户列表
	 * @param userType 用户类型字符串（"responder" 或 "reporter"）
	 * @param triggerRecordId 触发记录ID
	 * @param expectedSendTime 预期发送时间
	 * @param sendInfo 发送信息
	 * @param responseConfig 响应配置
	 * @param idExtractor 从用户对象中提取ID的函数
	 * @param nameExtractor 从用户对象中提取名称的函数
	 */
	private <T> void processUserList(List<T> userList, String userType, String triggerRecordId, Date expectedSendTime,
			ResponseConfig responseConfig, Function<T, String> idExtractor, Function<T, String> nameExtractor) {
		if (userList == null) {
			return;
		}
		userList.stream().filter(user -> user != null && StringUtils.hasText(idExtractor.apply(user))).forEach(user -> {
			try {
				createSingleUserSendRecord(triggerRecordId, expectedSendTime, idExtractor.apply(user),
						nameExtractor.apply(user), userType, responseConfig);
			}
			catch (Exception e) {
				logger.error("为用户 {} (类型: {}) 创建发送记录失败，触发记录ID: {}，错误: {}", idExtractor.apply(user), userType,
						triggerRecordId, e.getMessage(), e);
				// 记录单个用户创建失败的情况，但不影响其他用户
				createFailedSingleUserRecord(triggerRecordId, expectedSendTime, idExtractor.apply(user),
						nameExtractor.apply(user), userType, e);
			}
		});
	}

	/**
	 * 为单个用户创建发送记录
	 * @param triggerRecordId 触发记录ID
	 * @param expectedSendTime 预期发送时间
	 * @param sendInfo 发送信息
	 * @param userId 用户ID
	 * @param userName 用户名称
	 * @param userType 用户类型（responder/reporter）
	 * @param responseConfig 响应配置
	 */
	private void createSingleUserSendRecord(String triggerRecordId, Date expectedSendTime, String userId,
			String userName, String userType, ResponseConfig responseConfig) {
		List<SendUser> sendUsers = new ArrayList<>();

		SendUser sendUser = BuilderUtil.builder(SendUser::new)
			.with(SendUser::setSendUserId, userId)
			.with(SendUser::setSendUserName, userName)
			.with(SendUser::setSendUserType, userType)
			.build();
		sendUsers.add(sendUser);

		FeedbackTriggerSend sendRecord = BuilderUtil.builder(FeedbackTriggerSend::new)
			.with(FeedbackTriggerSend::setTriggerRecordId, triggerRecordId)
			.with(FeedbackTriggerSend::setExpectedSendTime, expectedSendTime)
			.with(FeedbackTriggerSend::setSendUser, sendUsers)
			.with(FeedbackTriggerSend::setResponseConfig, responseConfig)
			.with(FeedbackTriggerSend::setSendStatus, SendStatus.UNSENT)
			.build();

		repository.save(sendRecord);
	}

	/**
	 * 创建失败的发送记录（用于整个批次创建失败的情况）
	 * @param triggerRecord 触发记录
	 * @param responseConfig 响应配置
	 * @param e 异常信息
	 */
	private void createFailedSendRecord(FeedbackTriggerRecord triggerRecord, ResponseConfig responseConfig,
			Exception e) {
		try {
			Date expectedSendTime = calculateExpectedSendTime(triggerRecord.getTriggerTime(),
					responseConfig.getPointTime());
			FeedbackTriggerSend failedRecord = BuilderUtil.builder(FeedbackTriggerSend::new)
				.with(FeedbackTriggerSend::setTriggerRecordId, triggerRecord.getId())
				.with(FeedbackTriggerSend::setExpectedSendTime, expectedSendTime)
				.with(FeedbackTriggerSend::setSendResult, "创建失败: " + e.getMessage())
				.with(FeedbackTriggerSend::setSendInfo, "系统异常，创建发送记录失败")
				.with(FeedbackTriggerSend::setSendStatus, SendStatus.STOPPED)
				.build();
			repository.save(failedRecord);
		}
		catch (Exception saveException) {
			logger.error("保存失败记录时发生异常: {}", saveException.getMessage(), saveException);
		}
	}

	/**
	 * 创建单个用户的失败记录（用于单个用户创建失败的情况）
	 * @param triggerRecordId 触发记录ID
	 * @param expectedSendTime 预期发送时间
	 * @param userId 用户ID
	 * @param userName 用户名称
	 * @param userType 用户类型
	 * @param e 异常信息
	 */
	private void createFailedSingleUserRecord(String triggerRecordId, Date expectedSendTime, String userId,
			String userName, String userType, Exception e) {
		try {
			FeedbackTriggerSend failedRecord = BuilderUtil.builder(FeedbackTriggerSend::new)
				.with(FeedbackTriggerSend::setTriggerRecordId, triggerRecordId)
				.with(FeedbackTriggerSend::setExpectedSendTime, expectedSendTime)
				.with(FeedbackTriggerSend::setSendResult, "创建失败: " + e.getMessage())
				.with(FeedbackTriggerSend::setSendInfo, "系统异常，创建单个用户发送记录失败")
				.with(FeedbackTriggerSend::setSendStatus, SendStatus.STOPPED)
				.build();
			repository.save(failedRecord);
		}
		catch (Exception saveException) {
			logger.error("保存单个用户失败记录时发生异常: {}", saveException.getMessage(), saveException);
		}
	}

	/**
	 * 计算预期发送时间
	 * @param triggerTime 触发时间
	 * @param pointTime 分钟数
	 * @return 计算后的预期发送时间
	 */
	private Date calculateExpectedSendTime(Date triggerTime, Integer pointTime) {
		if (pointTime != null && pointTime > 0) {
			long pointTimeMillis = pointTime * 60 * 1000L;
			return new Date(triggerTime.getTime() + pointTimeMillis);
		}
		return triggerTime;
	}

	/**
	 * 构建发送信息
	 * @param triggerRecord 触发记录
	 * @param responseConfig 响应配置
	 * @return 发送信息
	 */
	private String buildSendInfo(FeedbackTriggerRecord triggerRecord, ResponseConfig responseConfig) {
		SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy年MM月dd日 HH时mm分ss秒");

		return """
				线体编码: %s
				异常名称：%s
				异常详情：%s
				触发时间: %s
				触发人: %s
				响应时间要求: %s""".formatted(triggerRecord.getLineCode(), triggerRecord.getAnomaliesName(),
				triggerRecord.getAnomaliesDetail(), dateFormat.format(triggerRecord.getTriggerTime()),
				triggerRecord.getTriggerUserName(), responseConfig.getPointTime() + "分钟内");
	}

	// ==================== 基础CRUD操作 ====================

	@Override
	public FeedbackTriggerSend save(FeedbackTriggerSend feedbackTriggerSend) {
		return repository.save(feedbackTriggerSend);
	}

	@Override
	public FeedbackTriggerSend findById(String id) {
		return repository.findById(id).orElseThrow(() -> new SystemException(SysErrEnum.NOT_FOUND));
	}

	@Override
	public PageList<FeedbackTriggerSend> page(PageableParam<FeedbackTriggerSend> param) {
		ExampleMatcher matcher = ExampleMatcher.matching()
			.withIgnoreNullValues()
			.withMatcher("triggerRecordId", GenericPropertyMatchers.exact())
			.withMatcher("sendStatus", GenericPropertyMatchers.exact())
			.withMatcher("sendResult", GenericPropertyMatchers.contains())
			.withMatcher("sendInfo", GenericPropertyMatchers.contains())
			.withIgnorePaths("_class");
		Example<FeedbackTriggerSend> example = Example.of(param.getSearchParams(), matcher);
		return PageList.fromPage(repository.findAll(example, param.getPageRequest()));
	}

	@Override
	public List<FeedbackTriggerSend> findList(FeedbackTriggerSend param) {
		ExampleMatcher matcher = ExampleMatcher.matching()
			.withIgnoreNullValues()
			.withMatcher("triggerRecordId", GenericPropertyMatchers.exact())
			.withMatcher("sendStatus", GenericPropertyMatchers.exact())
			.withMatcher("sendResult", GenericPropertyMatchers.contains())
			.withMatcher("sendInfo", GenericPropertyMatchers.contains());
		Example<FeedbackTriggerSend> example = Example.of(param, matcher);
		return repository.findAll(example);
	}

	@Override
	public List<FeedbackTriggerSend> findAll() {
		return mongoTemplate.findAll(FeedbackTriggerSend.class);
	}

	@Override
	public void deleteById(String id) {
		FeedbackTriggerSend record = findById(id);
		if (record != null) {
			repository.deleteById(id);
		}
	}

	@Override
	@Transactional
	public void batchDelete(List<String> ids) {
		if (ids != null && !ids.isEmpty()) {
			repository.deleteAllById(ids);
		}
	}

	// ==================== 业务查询方法 ====================

	@Override
	public List<FeedbackTriggerSend> findByTriggerRecordId(String triggerRecordId) {
		return repository.findByTriggerRecordId(triggerRecordId);
	}

	@Override
	public List<FeedbackTriggerSend> findBySendStatus(Boolean sendStatus) {
		return repository.findBySendStatus(sendStatus);
	}

	@Override
	public List<FeedbackTriggerSend> findBySendStatus(String sendStatus) {
		return repository.findBySendStatus(sendStatus);
	}

	@Override
	public List<FeedbackTriggerSend> findByTriggerRecordIdAndSendStatus(String triggerRecordId, Boolean sendStatus) {
		return repository.findByTriggerRecordIdAndSendStatus(triggerRecordId, sendStatus);
	}

	@Override
	public List<FeedbackTriggerSend> findByTriggerRecordIdAndSendStatus(String triggerRecordId, String sendStatus) {
		return repository.findByTriggerRecordIdAndSendStatus(triggerRecordId, sendStatus);
	}

	@Override
	public List<FeedbackTriggerSend> findPendingSendRecords(Date expectedSendTime) {
		return repository.findByExpectedSendTimeBeforeAndSendStatusFalse(expectedSendTime);
	}

	@Override
	public List<FeedbackTriggerSend> findByExpectedSendTimeBetween(Date startTime, Date endTime) {
		return repository.findByExpectedSendTimeBetween(startTime, endTime);
	}

	/**
	 * 构建个性化URL
	 * @param sendRecord 发送记录
	 * @param sendUser 发送用户
	 * @param triggerRecordId 触发记录ID
	 * @param sendRecordId 发送记录ID
	 * @return 个性化URL
	 */
	private String buildPersonalizedUrl(FeedbackTriggerSend sendRecord, SendUser sendUser, String triggerRecordId,
			String sendRecordId) {
		String action = USER_TYPE_RESPONDER.equals(sendUser.getSendUserType()) ? ACTION_EDIT : ACTION_VIEW;
		FeedbackUrlParams feedbackUrlParams = BuilderUtil.builder(FeedbackUrlParams::new)
			.with(FeedbackUrlParams::setAction, action)
			.with(FeedbackUrlParams::setTriggerRecordId, triggerRecordId)
			.with(FeedbackUrlParams::setSendRecordId, sendRecordId)
			.with(FeedbackUrlParams::setUserId, sendUser.getSendUserId())
			.with(FeedbackUrlParams::setUserName, sendUser.getSendUserName()) // 假设userName和userId相同
			.build();
		return feedbackConfig.buildFeedbackUrl(feedbackUrlParams);
	}

	/**
	 * 构建文本卡片
	 * @param sendUser 发送用户
	 * @param personalizedUrl 个性化URL
	 * @param sendInfo 发送信息
	 * @param sendUserType 发送用户类型
	 * @return 文本卡片
	 */
	private TextCard buildTextCard(SendUser sendUser, String personalizedUrl, String sendInfo, String sendUserType) {
		String buttonText = USER_TYPE_RESPONDER.equals(sendUserType) ? BUTTON_TEXT_SOLVE : BUTTON_TEXT_VIEW;

		TextCardContent content = new TextCardContent();
		content.setTitle(MESSAGE_TITLE);
		content.setDescription(sendInfo);
		content.setUrl(personalizedUrl);
		content.setBtntxt(buttonText);

		return new TextCard(sendUser.getSendUserId(), null, null, MESSAGE_TYPE_TEXTCARD, content,
				wxWorkConfig.getEnableIdTrans(), wxWorkConfig);
	}

	// ==================== 状态更新方法 ====================

	@Override
	@Transactional
	public FeedbackTriggerSend updateSendStatus(String id, Boolean sendStatus, String sendResult) {
		FeedbackTriggerSend record = findById(id);
		record.setSendStatus(SendStatus.fromBoolean(sendStatus));
		record.setSendResult(sendResult);
		if (sendStatus != null && sendStatus) {
			record.setSendTime(new Date());
		}
		return repository.save(record);
	}

	/**
	 * 更新发送状态（使用新的状态枚举）
	 * @param id 记录ID
	 * @param sendStatus 发送状态枚举
	 * @param sendResult 发送结果
	 * @return 更新后的记录
	 */
	@Transactional
	public FeedbackTriggerSend updateSendStatus(String id, SendStatus sendStatus, String sendResult) {
		FeedbackTriggerSend record = findById(id);
		record.setSendStatus(sendStatus);
		record.setSendResult(sendResult);
		if (sendStatus == SendStatus.SENT) {
			record.setSendTime(new Date());
		}
		return repository.save(record);
	}

	@Override
	@Transactional
	public FeedbackTriggerSend markAsSent(String id, String sendResult) {
		return updateSendStatus(id, true, sendResult);
	}

	@Override
	@Transactional
	public FeedbackTriggerSend markAsFailed(String id, String sendResult) {
		return updateSendStatus(id, false, sendResult);
	}

}
