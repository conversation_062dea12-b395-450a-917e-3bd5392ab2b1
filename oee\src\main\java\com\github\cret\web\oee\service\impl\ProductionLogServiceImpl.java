package com.github.cret.web.oee.service.impl;

import org.bson.Document;
import org.springframework.data.domain.Sort;
import org.springframework.data.domain.Sort.Direction;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;

import com.github.cret.web.common.domain.PageList;
import com.github.cret.web.common.domain.PageableParam;
import com.github.cret.web.oee.domain.log.LogRecord;
import com.github.cret.web.oee.domain.log.LogSearchParam;
import com.github.cret.web.oee.service.ProductionLogService;

@Service
public class ProductionLogServiceImpl implements ProductionLogService {

	private final MongoTemplate mongoTemplate;

	public ProductionLogServiceImpl(MongoTemplate mongoTemplate) {
		this.mongoTemplate = mongoTemplate;
	}

	@Override
	public PageList<LogRecord> page(PageableParam<LogSearchParam> param) {
		LogSearchParam searchParam = param.getSearchParams();

		Criteria criteria = new Criteria();
		if (searchParam.startTime() != null && searchParam.endTime() != null) {
			criteria.and("logtime").gte(searchParam.startTime()).lte(searchParam.endTime());
		}
		else if (searchParam.startTime() != null) {
			criteria.and("logtime").gte(searchParam.startTime());
		}
		else if (searchParam.endTime() != null) {
			criteria.and("logtime").lte(searchParam.endTime());
		}
		String collectionName = getCollectionName(searchParam.deviceCode());
		Long total = mongoTemplate.count(Query.query(criteria), collectionName);
		PageList<LogRecord> result = new PageList<>();
		result.setTotal(total);
		result.setList(mongoTemplate.find(
				Query.query(criteria).with(param.getPageRequest()).with(Sort.by(Direction.DESC, "logtime")),
				LogRecord.class, searchParam.deviceCode() + "_PRODUCTION"));
		return result;
	}

	@Override
	public Document get(String deviceCode, String id) {
		String collectionName = getCollectionName(deviceCode);
		return mongoTemplate.findOne(Query.query(Criteria.where("_id").is(id)), Document.class, collectionName);
	}

	private String getCollectionName(String deviceCode) {
		return deviceCode + "_PRODUCTION";
	}

}
