package com.github.cret.web.oee.enums;

/**
 * 发送状态
 */
public enum SendStatus {

	/**
	 * 未发送
	 */
	UNSENT("UNSENT", "未发送"),

	/**
	 * 已发送
	 */
	SENT("SENT", "已发送"),

	/**
	 * 停止发送
	 */
	STOPPED("STOPPED", "停止发送");

	private final String code;

	private final String description;

	SendStatus(String code, String description) {
		this.code = code;
		this.description = description;
	}

	public String getCode() {
		return code;
	}

	public String getDescription() {
		return description;
	}

	/**
	 * 根据代码获取枚举值
	 * @param code 状态代码
	 * @return 对应的枚举值，如果不存在则返回null
	 */
	public static SendStatus fromCode(String code) {
		if (code == null) {
			return null;
		}
		for (SendStatus status : values()) {
			if (status.code.equals(code)) {
				return status;
			}
		}
		return null;
	}

	/**
	 * 根据Boolean值获取枚举值
	 * @param sent Boolean值
	 * @return 对应的枚举值，如果为true则返回SENT，否则返回UNSENT
	 */
	public static SendStatus fromBoolean(Boolean sent) {
		if (sent == null || !sent) {
			return UNSENT;
		}
		return SENT;
	}

	/**
	 * 转换为Boolean值
	 * @return 如果为SENT则返回true，否则返回false
	 */
	public Boolean toBoolean() {
		return this == SENT;
	}

}
