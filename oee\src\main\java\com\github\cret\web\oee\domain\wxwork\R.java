package com.github.cret.web.oee.domain.wxwork;

import java.io.Serial;
import java.io.Serializable;

public class R<T> implements Serializable {

	@Serial
	private static final long serialVersionUID = 1L;

	private int code;

	private String msg;

	private T data;

	// 成功状态码
	private static final int SUCCESS_CODE = 200;

	// 失败状态码
	private static final int FAIL_CODE = 500;

	public static long getSerialversionuid() {
		return serialVersionUID;
	}

	public int getCode() {
		return code;
	}

	public void setCode(int code) {
		this.code = code;
	}

	public String getMsg() {
		return msg;
	}

	public void setMsg(String msg) {
		this.msg = msg;
	}

	public T getData() {
		return data;
	}

	public void setData(T data) {
		this.data = data;
	}

	/**
	 * 创建成功响应
	 * @param data 响应数据
	 * @param <T> 数据类型
	 * @return 成功响应对象
	 */
	public static <T> R<T> success(T data) {
		R<T> result = new R<>();
		result.setCode(SUCCESS_CODE);
		result.setMsg("操作成功");
		result.setData(data);
		return result;
	}

	/**
	 * 创建成功响应（无数据）
	 * @param <T> 数据类型
	 * @return 成功响应对象
	 */
	public static <T> R<T> success() {
		return success(null);
	}

	/**
	 * 创建成功响应（带自定义消息）
	 * @param data 响应数据
	 * @param message 自定义消息
	 * @param <T> 数据类型
	 * @return 成功响应对象
	 */
	public static <T> R<T> success(T data, String message) {
		R<T> result = new R<>();
		result.setCode(SUCCESS_CODE);
		result.setMsg(message);
		result.setData(data);
		return result;
	}

	/**
	 * 创建失败响应
	 * @param message 错误消息
	 * @param <T> 数据类型
	 * @return 失败响应对象
	 */
	public static <T> R<T> fail(String message) {
		R<T> result = new R<>();
		result.setCode(FAIL_CODE);
		result.setMsg(message);
		result.setData(null);
		return result;
	}

	/**
	 * 创建失败响应（带自定义错误码）
	 * @param code 错误码
	 * @param message 错误消息
	 * @param <T> 数据类型
	 * @return 失败响应对象
	 */
	public static <T> R<T> fail(int code, String message) {
		R<T> result = new R<>();
		result.setCode(code);
		result.setMsg(message);
		result.setData(null);
		return result;
	}

	/**
	 * 判断是否成功
	 * @return true表示成功，false表示失败
	 */
	public boolean isSuccess() {
		return this.code == SUCCESS_CODE;
	}

	/**
	 * 判断是否失败
	 * @return true表示失败，false表示成功
	 */
	public boolean isFail() {
		return !isSuccess();
	}

}
