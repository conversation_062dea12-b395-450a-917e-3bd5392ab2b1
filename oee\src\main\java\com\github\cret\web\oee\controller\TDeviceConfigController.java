package com.github.cret.web.oee.controller;

import com.github.cret.web.common.domain.PageableParam;
import com.github.cret.web.oee.document.TDeviceConfig;
import com.github.cret.web.oee.service.TDeviceConfigService;
import jakarta.annotation.Resource;
import com.github.cret.web.common.domain.PageList;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/tDeviceConfig")
public class TDeviceConfigController {

	@Resource
	private TDeviceConfigService tDeviceConfigService;

	/**
	 * 获取列表(分页)
	 */
	@PostMapping("/page")
	public PageList<TDeviceConfig> page(@RequestBody PageableParam<String> param) {
		return tDeviceConfigService.page(param);
	}

	/**
	 * 获取
	 */
	@GetMapping("/{id}")
	public TDeviceConfig get(@PathVariable String id) {
		return tDeviceConfigService.findById(id);
	}

	/**
	 * 添加
	 */
	@PostMapping("/add")
	public void add(@RequestBody TDeviceConfig tDeviceConfig) {
		tDeviceConfigService.save(tDeviceConfig);
	}

	/**
	 * 修改
	 */
	@PutMapping("/{id}")
	public void update(@PathVariable String id, @RequestBody TDeviceConfig tDeviceConfig) {
		tDeviceConfig.setId(id);
		tDeviceConfigService.save(tDeviceConfig);
	}

	/**
	 * 删除
	 */
	@DeleteMapping("/{id}")
	public void delete(@PathVariable String id) {
		tDeviceConfigService.deleteById(id);
	}

}
