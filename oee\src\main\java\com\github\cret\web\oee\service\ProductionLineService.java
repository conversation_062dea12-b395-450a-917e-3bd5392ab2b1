package com.github.cret.web.oee.service;

import java.util.List;

import com.github.cret.web.oee.document.ProductionLine;

/**
 * 生产线服务接口，提供与生产线相关的业务逻辑操作。
 */
public interface ProductionLineService {

	/**
	 * 获取所有生产线的列表。
	 * @return 返回所有生产线的列表。
	 */
	List<ProductionLine> getList();

	/**
	 * 根据车间编码查找对应的生产线列表。
	 * @param workShopId 车间ID
	 * @return 返回与指定车间ID相关的生产线列表。
	 */
	List<ProductionLine> findProdutionLineByWorkshopCode(String workShopId);

	/**
	 * 根据生产线代码查找对应的生产线。
	 * @param code 生产线代码
	 * @return 返回与指定代码匹配的生产线对象。
	 */
	ProductionLine findProductionLineBycode(String code);

}