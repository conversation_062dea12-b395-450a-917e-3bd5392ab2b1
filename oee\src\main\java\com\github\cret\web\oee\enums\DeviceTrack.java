package com.github.cret.web.oee.enums;

/**
 * 设备轨道号
 */
public enum DeviceTrack {

	TRACK1("轨道1", "track1"), // 轨道1
	TRACK2("轨道2", "track2"); // 轨道2

	private final String name;

	private final String code;

	DeviceTrack(String name, String code) {
		this.name = name;
		this.code = code;
	}

	public String getName() {
		return name;
	}

	public String getCode() {
		return code;
	}

	// 根据 code 获取对应的 DeviceTrack 枚举
	public static DeviceTrack getByCode(String code) {
		for (DeviceTrack track : values()) {
			if (track.getCode().equals(code)) {
				return track;
			}
		}
		return null; // 如果没有匹配的 code，返回 null
	}

	@Override
	public String toString() {
		return "DeviceTrack{" + "name='" + name + '\'' + ", code='" + code + '\'' + '}';
	}

}