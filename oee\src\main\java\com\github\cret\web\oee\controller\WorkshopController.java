package com.github.cret.web.oee.controller;

import java.util.List;

import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.github.cret.web.oee.document.Workshop;
import com.github.cret.web.oee.service.WorkshopService;

@RestController
@RequestMapping("/workshop")
public class WorkshopController {

	private final WorkshopService workshopService;

	public WorkshopController(WorkshopService workshopService) {
		this.workshopService = workshopService;
	}

	/**
	 * 获取车间列表
	 * @return 车间列表
	 */

	@RequestMapping("/list")
	public List<Workshop> getAllWorkshops() {
		return workshopService.getList();
	}

	/**
	 * 创建车间
	 * @param workshop 车间
	 * @return 车间
	 */
	@PostMapping
	public Workshop createWorkshop(@RequestBody Workshop workshop) {
		return workshopService.saveWorkshop(workshop);
	}

}
