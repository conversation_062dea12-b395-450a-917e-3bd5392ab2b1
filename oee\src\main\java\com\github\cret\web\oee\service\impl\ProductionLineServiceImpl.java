package com.github.cret.web.oee.service.impl;

import java.util.List;

import org.springframework.stereotype.Service;

import com.github.cret.web.oee.document.ProductionLine;
import com.github.cret.web.oee.repository.ProductionLineRepository;
import com.github.cret.web.oee.service.ProductionLineService;

@Service
public class ProductionLineServiceImpl implements ProductionLineService {

	private final ProductionLineRepository repository;

	public ProductionLineServiceImpl(ProductionLineRepository repository) {
		this.repository = repository;
	}

	@Override
	public List<ProductionLine> getList() {
		List<ProductionLine> list = repository.findAll();
		return list.stream().filter(e -> e.getEnable() == 1).toList();
	}

	@Override
	public List<ProductionLine> findProdutionLineByWorkshopCode(String workShopId) {
		List<ProductionLine> list = repository.findByWorkshopCode(workShopId);
		return list.stream().filter(e -> e.getEnable() == 1).toList();
	}

	@Override
	public ProductionLine findProductionLineBycode(String code) {
		ProductionLine productionLine = repository.findByCode(code)
			.orElseThrow(() -> new RuntimeException("未找到代码为 " + code + " 的生产线"));
		if (0 == productionLine.getEnable()) {
			throw new RuntimeException("未找到代码为 " + code + " 的生产线");
		}
		return productionLine;
	}

}
