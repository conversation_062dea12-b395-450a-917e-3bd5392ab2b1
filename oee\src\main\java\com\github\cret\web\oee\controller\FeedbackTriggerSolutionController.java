package com.github.cret.web.oee.controller;

import com.github.cret.web.common.domain.PageList;
import com.github.cret.web.common.domain.PageableParam;
import com.github.cret.web.oee.document.feedback.FeedbackTriggerSolution;
import com.github.cret.web.oee.domain.query.FeedbackTriggerSolutionQuery;
import com.github.cret.web.oee.domain.request.SolutionRejectRequest;
import com.github.cret.web.oee.service.FeedbackTriggerSolutionService;

import java.util.List;

import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/feedback-trigger-solutions")
public class FeedbackTriggerSolutionController {

	private final FeedbackTriggerSolutionService service;

	public FeedbackTriggerSolutionController(FeedbackTriggerSolutionService service) {
		this.service = service;
	}

	/**
	 * 创建解决方案
	 * @param solution 解决方案数据
	 * @return 创建后的解决方案
	 */
	@PostMapping
	public FeedbackTriggerSolution create(@RequestBody FeedbackTriggerSolution solution) {
		return service.save(solution);
	}

	/**
	 * 删除解决方案
	 * @param id 解决方案ID
	 */
	@DeleteMapping("/{id}")
	public void delete(@PathVariable String id) {
		service.delete(id);
	}

	/**
	 * 根据ID获取解决方案
	 * @param id 解决方案ID
	 * @return 解决方案
	 */
	@GetMapping("/{id}")
	public FeedbackTriggerSolution getById(@PathVariable String id) {
		return service.findById(id);
	}

	/**
	 * 分页搜索解决方案
	 * @param query 查询条件
	 * @return 分页结果
	 */
	@PostMapping("/page")
	public PageList<FeedbackTriggerSolution> search(@RequestBody PageableParam<FeedbackTriggerSolutionQuery> query) {
		return service.search(query);
	}

	/**
	 * 更新解决方案
	 * @param id 解决方案ID
	 * @param solution 解决方案数据
	 * @return 更新后的解决方案
	 */
	@PutMapping("/{id}")
	public FeedbackTriggerSolution update(@PathVariable String id, @RequestBody FeedbackTriggerSolution solution) {
		return service.update(id, solution);
	}

	/**
	 * 根据触发记录ID获取解决方案列表
	 * @param triggerRecordId 触发记录ID
	 * @return 解决方案列表
	 */
	@GetMapping("/by-trigger-record/{triggerRecordId}")
	public List<FeedbackTriggerSolution> getByTriggerRecordId(@PathVariable String triggerRecordId) {
		return service.findByTriggerRecordId(triggerRecordId);
	}

	/**
	 * 根据解决人ID获取解决方案列表
	 * @param solverId 解决人ID
	 * @return 解决方案列表
	 */
	@GetMapping("/by-solver/{solverId}")
	public List<FeedbackTriggerSolution> getBySolverId(@PathVariable String solverId) {
		return service.findBySolverId(solverId);
	}

	/**
	 * 提交解决方案
	 * @param id 解决方案ID
	 * @return 提交后的解决方案
	 */
	@PutMapping("/{id}/submit")
	public FeedbackTriggerSolution submit(@PathVariable String id) {
		return service.submit(id);
	}

	/**
	 * 根据触发记录ID和发送记录ID获取唯一解决方案
	 * @param triggerRecordId 触发记录ID
	 * @param triggerSendId 发送记录ID
	 * @return 解决方案（唯一对象）
	 */
	@GetMapping("/by-trigger-record-and-send")
	public FeedbackTriggerSolution getByTriggerRecordIdAndTriggerSendId(@RequestParam String triggerRecordId,
			@RequestParam String triggerSendId) {
		return service.findByTriggerRecordIdAndTriggerSendId(triggerRecordId, triggerSendId);
	}

	/**
	 * 退回解决方案
	 * @param id 解决方案ID
	 * @param request 退回请求对象，包含退回原因、退回人ID和退回人名称
	 * @return 退回后的解决方案
	 */
	@PutMapping("/{id}/reject")
	public FeedbackTriggerSolution reject(@PathVariable String id, @RequestBody SolutionRejectRequest request) {
		return service.reject(id, request);
	}

}
