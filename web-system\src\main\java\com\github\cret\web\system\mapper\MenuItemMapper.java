package com.github.cret.web.system.mapper;

import org.mapstruct.Mapper;
import org.mapstruct.MappingConstants;
import org.mapstruct.ReportingPolicy;

import com.github.cret.web.system.document.MenuItem;
import com.github.cret.web.system.domain.MenuItemCreate;

@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE, componentModel = MappingConstants.ComponentModel.SPRING)
public interface MenuItemMapper {

	MenuItem toEntity(MenuItemCreate menuItemCreate);

}