package com.github.cret.web.oee.document;

import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

/**
 * 不良类型字典表文档
 */
@Document("t_bad_item")
public class BadItemDoc {

	@Id
	private String id;

	/**
	 * 不良项目ID
	 */
	@Field(name = "BAD_ITEM_ID")
	private String badItemId;

	/**
	 * 不良项目名称
	 */
	@Field(name = "BAD_ITEM_NAME")
	private String badItemName;

	/**
	 * 不良类型ID
	 */
	@Field(name = "BAD_TYPE_ID")
	private String badTypeId;

	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	}

	public String getBadItemId() {
		return badItemId;
	}

	public void setBadItemId(String badItemId) {
		this.badItemId = badItemId;
	}

	public String getBadItemName() {
		return badItemName;
	}

	public void setBadItemName(String badItemName) {
		this.badItemName = badItemName;
	}

	public String getBadTypeId() {
		return badTypeId;
	}

	public void setBadTypeId(String badTypeId) {
		this.badTypeId = badTypeId;
	}

	@Override
	public String toString() {
		return "BadItemDoc{" + "id='" + id + '\'' + ", badItemId='" + badItemId + '\'' + ", badItemName='" + badItemName
				+ '\'' + ", badTypeId='" + badTypeId + '\'' + '}';
	}

}
