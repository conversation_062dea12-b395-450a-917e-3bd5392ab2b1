package com.github.cret.web.oee.service.impl;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Example;
import org.springframework.data.domain.ExampleMatcher;
import org.springframework.data.domain.ExampleMatcher.GenericPropertyMatchers;
import org.springframework.stereotype.Service;

import com.github.cret.web.common.domain.PageList;
import com.github.cret.web.common.domain.PageableParam;
import com.github.cret.web.oee.document.event.NpmEventCode;
import com.github.cret.web.oee.enums.EventType;
import com.github.cret.web.oee.repository.NpmEventCodeRepository;
import com.github.cret.web.oee.service.NpmEventCodeService;

/**
 * 松下贴片机事件代码服务实现类
 */
@Service
public class NpmEventCodeServiceImpl implements NpmEventCodeService {

	@Autowired
	private NpmEventCodeRepository repository;

	public NpmEventCode save(NpmEventCode npmEventCode) {
		return repository.save(npmEventCode);
	}

	public List<NpmEventCode> findAll() {
		return repository.findAll();
	}

	public NpmEventCode findById(String id) {
		return repository.findById(id).orElse(null);
	}

	@Override
	public PageList<NpmEventCode> page(PageableParam<NpmEventCode> param) {
		ExampleMatcher matcher = ExampleMatcher.matching()
			.withIgnoreNullValues()
			.withMatcher("mainCode", GenericPropertyMatchers.exact())
			.withMatcher("subCode", GenericPropertyMatchers.exact())
			.withMatcher("eventCode", GenericPropertyMatchers.exact());
		Example<NpmEventCode> example = Example.of(param.getSearchParams(), matcher);
		return PageList.fromPage(repository.findAll(example, param.getPageRequest()));
	}

	public NpmEventCode update(NpmEventCode npmEventCode) {
		return repository.save(npmEventCode);
	}

	public void deleteById(String id) {
		repository.deleteById(id);
	}

	// 自定义查询方法
	public List<NpmEventCode> findByMainCode(String mainCode) {
		return repository.findByMainCode(mainCode);
	}

	public List<NpmEventCode> findByEventType(EventType eventType) {
		return repository.findByEventType(eventType);
	}

}
