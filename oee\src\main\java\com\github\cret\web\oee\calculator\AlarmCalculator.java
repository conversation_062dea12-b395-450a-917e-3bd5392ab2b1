package com.github.cret.web.oee.calculator;

import java.util.List;
import java.util.Map;

import com.github.cret.web.oee.domain.analyze.ProductionData;

/**
 * 异常计算器
 */
public class AlarmCalculator {

	/**
	 * 依据瓶颈工序获取松下贴片机软件停止时间
	 * @param productData
	 * @return
	 */
	public static Double getNpmsmtAlarm(Map<String, List<ProductionData>> productData) {
		Double result = 0.0;
		for (Map.Entry<String, List<ProductionData>> entry : productData.entrySet()) {
			// 尝试获取瓶颈设备的运行时间
			Double bottleneckRuntime = entry.getValue()
				.stream()
				.filter(data -> data != null && Boolean.TRUE.equals(data.getBottleneck()))
				.map(e -> {
					return e.getScStopTime() + e.getScEmergencyStopTime();
				})
				.findFirst()
				.orElse(0.0);
			result += bottleneckRuntime;

		}

		return result;
	}

}
