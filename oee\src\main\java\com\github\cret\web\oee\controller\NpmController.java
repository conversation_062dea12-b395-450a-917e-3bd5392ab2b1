package com.github.cret.web.oee.controller;

import java.util.List;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.github.cret.web.oee.domain.npm.LoginRes;
import com.github.cret.web.oee.domain.npm.ReportRes;
import com.github.cret.web.oee.service.NpmService;

@RestController
@RequestMapping("/Npm")
public class NpmController {

	private final NpmService npmService;

	public NpmController(NpmService npmService) {
		this.npmService = npmService;
	}

	@GetMapping("/login")
	public LoginRes login() {
		return npmService.getLogin().block();
	}

	@GetMapping("/ReferProductManageReportAction")
	public List<ReportRes> ReferProductManageReportAction() {
		return npmService.getReferProductManageReportAction().block();
	}

	@GetMapping("/ReferProductManageReportDtlAction")
	public String ReferProductManageReportDtlAction(String listindex) {
		return npmService.getReferProductManageReportDtlAction(listindex).block();
	}

}
