package com.github.cret.web.oee.domain.deviceConfig;

import org.springframework.data.mongodb.core.mapping.Field;

public class Input {

	@Field(name = "dateApi")
	private String dateApi;

	@Field(name = "ignoredays")
	private Integer ignoredays;

	@Field(name = "interval")
	private Integer interval;

	@Field(name = "path")
	private String path;

	@Field(name = "pattern")
	private String pattern;

	@Field(name = "type")
	private String type;

	public String getDateApi() {
		return dateApi;
	}

	public void setDateApi(String dateApi) {
		this.dateApi = dateApi;
	}

	public Integer getIgnoredays() {
		return ignoredays;
	}

	public void setIgnoredays(Integer ignoredays) {
		this.ignoredays = ignoredays;
	}

	public Integer getInterval() {
		return interval;
	}

	public void setInterval(Integer interval) {
		this.interval = interval;
	}

	public String getPath() {
		return path;
	}

	public void setPath(String path) {
		this.path = path;
	}

	public String getPattern() {
		return pattern;
	}

	public void setPattern(String pattern) {
		this.pattern = pattern;
	}

	public String getType() {
		return type;
	}

	public void setType(String type) {
		this.type = type;
	}

}
