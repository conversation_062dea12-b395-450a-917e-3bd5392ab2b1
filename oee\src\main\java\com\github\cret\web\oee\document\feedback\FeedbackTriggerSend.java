package com.github.cret.web.oee.document.feedback;

import java.util.Date;
import java.util.List;

import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

/**
 * 快返触发发送表
 */
@Document("t_feedback_trigger_send")
public class FeedbackTriggerSend {

	@Id
	private String id;

	// 预期发送时间
	@Field(name = "expected_send_time")
	private Date expectedSendTime;

	// 发送时间
	@Field(name = "send_time")
	private Date sendTime;

	// 发送人
	@Field(name = "send_user")
	private List<SendUser> sendUser;

	// 发送结果
	@Field(name = "send_result")
	private String sendResult;

	// 触发记录ID
	@Field(name = "trigger_record_id")
	private String triggerRecordId;

	// 发送信息
	@Field(name = "send_info")
	private String sendInfo;

	// 发送状态
	@Field(name = "send_status")
	private SendStatus sendStatus;

	// 响应配置
	@Field(name = "response_config")
	private ResponseConfig responseConfig;

	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	}

	public Date getExpectedSendTime() {
		return expectedSendTime;
	}

	public void setExpectedSendTime(Date expectedSendTime) {
		this.expectedSendTime = expectedSendTime;
	}

	public Date getSendTime() {
		return sendTime;
	}

	public void setSendTime(Date sendTime) {
		this.sendTime = sendTime;
	}

	public List<SendUser> getSendUser() {
		return sendUser;
	}

	public void setSendUser(List<SendUser> sendUser) {
		this.sendUser = sendUser;
	}

	public String getSendResult() {
		return sendResult;
	}

	public void setSendResult(String sendResult) {
		this.sendResult = sendResult;
	}

	public String getTriggerRecordId() {
		return triggerRecordId;
	}

	public void setTriggerRecordId(String triggerRecordId) {
		this.triggerRecordId = triggerRecordId;
	}

	public String getSendInfo() {
		return sendInfo;
	}

	public void setSendInfo(String sendInfo) {
		this.sendInfo = sendInfo;
	}

	public SendStatus getSendStatus() {
		return sendStatus;
	}

	public void setSendStatus(SendStatus sendStatus) {
		this.sendStatus = sendStatus;
	}

	public ResponseConfig getResponseConfig() {
		return responseConfig;
	}

	public void setResponseConfig(ResponseConfig responseConfig) {
		this.responseConfig = responseConfig;
	}

}
