package com.github.cret.web.oee.service.impl;

import java.util.Date;
import java.util.List;
import java.util.Optional;

import org.springframework.data.domain.Limit;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;

import com.github.cret.web.common.domain.PageList;
import com.github.cret.web.common.domain.PageableParam;
import com.github.cret.web.oee.document.analyze.Abnormal;
import com.github.cret.web.oee.domain.abnormal.AbnormalConfirm;
import com.github.cret.web.oee.domain.abnormal.AbnormalSearch;
import com.github.cret.web.oee.repository.AbnormalRepository;
import com.github.cret.web.oee.service.AbnormalService;

@Service
public class AbnormalServiceImpl implements AbnormalService {

	private final AbnormalRepository abnormalRepository;

	private final MongoTemplate mongoTemplate;

	public AbnormalServiceImpl(AbnormalRepository abnormalRepository, MongoTemplate mongoTemplate) {
		this.abnormalRepository = abnormalRepository;
		this.mongoTemplate = mongoTemplate;
	}

	@Override
	public Abnormal save(Abnormal abnormal) {
		// 保存时设置写入时间
		if (abnormal.getWriteTime() == null) {
			abnormal.setWriteTime(new Date());
		}

		abnormal.setDuration(abnormal.getEndTime().getTime() - abnormal.getStartTime().getTime());
		return abnormalRepository.save(abnormal);
	}

	@Override
	public Abnormal confirm(String id, AbnormalConfirm confirm) {
		Optional<Abnormal> byId = abnormalRepository.findById(id);
		return byId.map(e -> {
			e.setConfirm(confirm.getConfirm());
			e.setConfirmTime(new Date());
			return abnormalRepository.save(e);
		}).orElse(null);
	}

	@Override
	public PageList<Abnormal> page(PageableParam<AbnormalSearch> param) {
		AbnormalSearch search = param.getSearchParams();
		Criteria criteria = new Criteria();
		Integer pageSize = param.getPageData().getPageSize();
		Integer pageNum = param.getPageData().getPageNumber();
		// 为每个字段构建条件
		if (search.getLineCode() != null) {
			criteria.and("lineCode").is(search.getLineCode());
		}
		if (search.getClassification() != null) {
			criteria.and("classification").is(search.getClassification());
		}
		if (search.getCause() != null) {
			criteria.and("cause").regex(".*" + search.getCause() + ".*");
		}
		if (search.getResponsible() != null) {
			criteria.and("responsible").regex(".*" + search.getResponsible() + ".*");
		}
		if (search.getDepartment() != null) {
			criteria.and("department").regex(".*" + search.getDepartment() + ".*");
		}
		if (search.getWriter() != null) {
			criteria.and("writer").regex(".*" + search.getWriter() + ".*");
		}
		if (search.getConfirm() != null) {
			criteria.and("confirm").regex(".*" + search.getConfirm() + ".*");
		}

		// 添加写入时间范围条件
		if (search.getWriteStartTime() != null || search.getWriteEndTime() != null) {
			criteria.and("writeTime").gte(search.getWriteStartTime()).lte(search.getWriteEndTime());
		}

		// 添加确认时间范围条件
		if (search.getConfirmStartTime() != null || search.getConfirmEndTime() != null) {
			criteria.and("confirmTime").gte(search.getConfirmStartTime()).lte(search.getConfirmEndTime());
		}

		// 设置默认排序字段
		if (param.getPageData().getSortField() == null) {
			param.getPageData().setSortField("writeTime");
			param.getPageData().setSortDirection("DESC");
		}

		Query query = Query.query(criteria);
		query.with(param.getPageRequest());

		long total = mongoTemplate.count(query.skip(0).limit(0), Abnormal.class);
		List<Abnormal> content = mongoTemplate.find(query.skip(pageNum * pageSize).limit(Limit.of(pageSize)),
				Abnormal.class);

		PageList<Abnormal> pageList = new PageList<Abnormal>();
		pageList.setTotal(total);
		pageList.setList(content);

		// 计算是否有下一页
		int currentPage = param.getPageRequest().getPageNumber();
		boolean hasNext = (currentPage + 1) * pageSize < total;
		pageList.setHasNext(hasNext);

		return pageList;
	}

	@Override
	public Abnormal findById(String id) {
		return abnormalRepository.findById(id).orElse(null);
	}

	@Override
	public void deleteById(String id) {
		abnormalRepository.deleteById(id);
	}

	@Override
	public List<Abnormal> findList(AbnormalSearch param) {
		Criteria criteria = new Criteria();
		// 为每个字段构建条件
		if (param.getLineCode() != null) {
			criteria.and("lineCode").is(param.getLineCode());
		}
		if (param.getClassification() != null) {
			criteria.and("classification").is(param.getClassification());
		}
		if (param.getCause() != null) {
			criteria.and("cause").regex(".*" + param.getCause() + ".*");
		}
		if (param.getResponsible() != null) {
			criteria.and("responsible").regex(".*" + param.getResponsible() + ".*");
		}
		if (param.getDepartment() != null) {
			criteria.and("department").regex(".*" + param.getDepartment() + ".*");
		}
		if (param.getWriter() != null) {
			criteria.and("writer").regex(".*" + param.getWriter() + ".*");
		}
		if (param.getConfirm() != null) {
			criteria.and("confirm").regex(".*" + param.getConfirm() + ".*");
		}
		// 写入时间范围
		if (param.getWriteStartTime() != null || param.getWriteEndTime() != null) {
			criteria.and("writeTime").gte(param.getWriteStartTime()).lte(param.getWriteEndTime());
		}
		// 确认时间范围
		if (param.getConfirmStartTime() != null || param.getConfirmEndTime() != null) {
			criteria.and("confirmTime").gte(param.getConfirmStartTime()).lte(param.getConfirmEndTime());
		}
		Query query = Query.query(criteria);
		// 先按线体编码升序，再按写入时间升序
		query.with(Sort.by(Sort.Direction.ASC, "lineCode").and(Sort.by(Sort.Direction.ASC, "writeTime")));
		return mongoTemplate.find(query, Abnormal.class);
	}

}
