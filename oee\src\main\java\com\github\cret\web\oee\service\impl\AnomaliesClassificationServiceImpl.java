package com.github.cret.web.oee.service.impl;

import java.util.List;

import org.springframework.data.domain.Limit;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import com.github.cret.web.common.domain.PageList;
import com.github.cret.web.common.domain.PageableParam;
import com.github.cret.web.oee.document.feedback.AnomaliesClassification;
import com.github.cret.web.oee.domain.anomalies.AnomaliesClassificationSearch;
import com.github.cret.web.oee.repository.AnomaliesClassificationRepository;
import com.github.cret.web.oee.service.AnomaliesClassificationService;

import jakarta.annotation.Resource;

@Service
public class AnomaliesClassificationServiceImpl implements AnomaliesClassificationService {

	@Resource
	private AnomaliesClassificationRepository anomaliesClassificationRepository;

	@Resource
	private MongoTemplate mongoTemplate;

	@Override
	public void save(AnomaliesClassification anomaliesClassification) {
		anomaliesClassificationRepository.save(anomaliesClassification);
	}

	@Override
	public void deleteById(String id) {
		anomaliesClassificationRepository.deleteById(id);
	}

	@Override
	public AnomaliesClassification findById(String id) {
		return anomaliesClassificationRepository.findById(id).orElse(null);
	}

	@Override
	public PageList<AnomaliesClassification> page(PageableParam<AnomaliesClassificationSearch> param) {

		PageList<AnomaliesClassification> pageList = new PageList<>();
		AnomaliesClassificationSearch searchParams = param.getSearchParams();
		String anomaliesCode = searchParams.getAnomaliesCode();
		String lineCode = searchParams.getLineCode();

		Criteria criteria = new Criteria();
		Integer pageSize = param.getPageData().getPageSize();
		Integer pageNum = param.getPageData().getPageNumber();

		if (StringUtils.hasText(lineCode)) {
			criteria.and("line_code").is(lineCode);
		}

		// 模糊查询异常代码
		if (StringUtils.hasText(anomaliesCode)) {
			criteria.and("anomalies_code").is(anomaliesCode);
		}

		// 设置默认排序字段
		if (param.getPageData().getSortField() == null) {
			param.getPageData().setSortField("line_code");
		}

		Query query = Query.query(criteria);
		query.with(param.getPageRequest());

		long total = mongoTemplate.count(query.skip(0).limit(0), AnomaliesClassification.class);
		List<AnomaliesClassification> content = mongoTemplate
			.find(query.skip((long) pageNum * pageSize).limit(Limit.of(pageSize)), AnomaliesClassification.class);

		pageList.setTotal(total);
		pageList.setList(content);

		// 计算是否有下一页
		int currentPage = param.getPageRequest().getPageNumber();
		boolean hasNext = (long) (currentPage + 1) * pageSize < total;
		pageList.setHasNext(hasNext);

		return pageList;
	}

	@Override
	public void updateEnable(String id, Boolean enable) {
		anomaliesClassificationRepository.findById(id).ifPresent(e -> {
			e.setEnable(enable);
			anomaliesClassificationRepository.save(e);
		});
	}

	@Override
	public AnomaliesClassification findByLineCodeAndAnomaliesCode(String lineCode, String anomaliesCode) {
		return anomaliesClassificationRepository.findByLineCodeAndAnomaliesCode(lineCode, anomaliesCode).orElse(null);
	}

	@Override
	public AnomaliesClassification findByLineCodeAndAnomaliesCodeAndEnable(String lineCode, String anomaliesCode,
			Boolean enable) {
		return anomaliesClassificationRepository
			.findByLineCodeAndAnomaliesCodeAndEnable(lineCode, anomaliesCode, enable)
			.orElse(null);
	}

}
