package com.github.cret.web.common.enumerate;

import com.github.cret.web.common.exception.SystemException;
import org.springframework.http.HttpStatus;

public enum SysErrEnum {

	NOT_FOUND("10404", "不存在的对象", HttpStatus.NOT_FOUND),
	UNKNOWN_ERROR("19999", "未知错误", HttpStatus.INTERNAL_SERVER_ERROR),
	UNAUTHORIZED("10401", "未登录", HttpStatus.UNAUTHORIZED),
	NETWORK_ERROR("10503", "网络错误", HttpStatus.SERVICE_UNAVAILABLE),
	INVALID_PARAMETER("10400", "无效的参数", HttpStatus.BAD_REQUEST), LOGIN_FAIL("20002", "登录失败", HttpStatus.BAD_REQUEST),
	CONFLICT_KEY("20001", "唯一属性冲突", HttpStatus.BAD_REQUEST),
	SYSTEM_ERROR("20003", "系统错误", HttpStatus.INTERNAL_SERVER_ERROR);

	private final String code;

	private final String msg;

	private final HttpStatus status;

	SysErrEnum(String code, String msg, HttpStatus status) {
		this.code = code;
		this.msg = msg;
		this.status = status;
	}

	public SystemException exception() {
		return new SystemException(this);
	}

	public SystemException exception(String message) {
		return new SystemException(this, message);
	}

	public SystemException exception(String message, Exception e) {
		return new SystemException(this, message);
	}

	public SystemException exception(Exception e) {
		return new SystemException(this, e.getMessage());
	}

	public String getCode() {
		return this.code;
	}

	public String getMsg() {
		return this.msg;
	}

	public HttpStatus getHttpStatus() {
		return this.status;
	}

}
