package com.github.cret.web.oee.service.impl;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Date;
import java.util.List;
import java.util.Optional;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.github.cret.web.oee.client.MesService;
import com.github.cret.web.oee.document.ProductionLine;
import com.github.cret.web.oee.document.mes.LabelWorkProcessAnalysis;
import com.github.cret.web.oee.repository.LabelWorkProcessAnalysisRepository;
import com.github.cret.web.oee.service.LabelWorkProcessAnalysisService;
import com.github.cret.web.oee.service.ProductionLineService;

@Service
public class LabelWorkProcessAnalysisServiceImpl implements LabelWorkProcessAnalysisService {

	private static final Logger logger = LoggerFactory.getLogger(LabelWorkProcessAnalysisServiceImpl.class);

	private final LabelWorkProcessAnalysisRepository repository;

	private final MesService mesService;

	private final ProductionLineService productionLineService;

	public LabelWorkProcessAnalysisServiceImpl(LabelWorkProcessAnalysisRepository repository, MesService mesService,
			ProductionLineService productionLineService) {
		this.repository = repository;
		this.mesService = mesService;
		this.productionLineService = productionLineService;
	}

	@Override
	public List<LabelWorkProcessAnalysis> getLabelWorkProcessAnalysis(String plId, Date startDate, Date endDate) {
		return repository.findByPlIdAndFinalWpCmpDateBetween(plId, startDate, endDate);
	}

	@Override
	public int syncLabelWorkProcessAnalysis(String plId, Date startDate, Date endDate) {
		try {
			logger.info("开始同步线体 {} 的标签工艺过程分析数据，时间范围：{} - {}", plId, startDate, endDate);

			// 调用MES接口获取数据
			List<LabelWorkProcessAnalysis> mesData = mesService.fetchLabelWorkProcessAnalysis(plId, startDate, endDate);

			if (mesData == null || mesData.isEmpty()) {
				logger.info("线体 {} 在指定时间范围内没有标签工艺过程分析数据", plId);
				return 0;
			}

			// 批量保存或更新数据
			int savedCount = batchSaveOrUpdate(mesData);
			logger.info("线体 {} 同步完成，共处理 {} 条数据", plId, savedCount);

			return savedCount;
		}
		catch (Exception e) {
			logger.error("同步线体 {} 的标签工艺过程分析数据失败", plId, e);
			return 0;
		}
	}

	@Override
	public int syncAllLinesCurrentDayData() {
		logger.info("开始同步所有线体当天的标签工艺过程分析数据");

		// 获取当天的时间范围
		LocalDate today = LocalDate.now();
		LocalDateTime startOfDay = today.atTime(8, 0, 0); // 早班8点开始
		LocalDateTime endOfDay = today.plusDays(1).atTime(8, 0, 0); // 次日早班8点结束

		Date startDate = Date.from(startOfDay.atZone(ZoneId.systemDefault()).toInstant());
		Date endDate = Date.from(endOfDay.atZone(ZoneId.systemDefault()).toInstant());

		// 获取所有启用的生产线
		List<ProductionLine> productionLines = productionLineService.getList()
			.stream()
			.filter(line -> line.getEnable() != null && line.getEnable() == 1)
			.toList();

		int totalSyncCount = 0;

		for (ProductionLine line : productionLines) {
			try {
				// 线体间查询间隔，避免短时间大量查询
				if (totalSyncCount > 0) {
					Thread.sleep(2000); // 间隔2秒
				}

				int syncCount = syncLabelWorkProcessAnalysis(line.getCode(), startDate, endDate);
				totalSyncCount += syncCount;

			}
			catch (InterruptedException e) {
				logger.warn("线体间查询间隔被中断", e);
				Thread.currentThread().interrupt();
			}
			catch (Exception e) {
				logger.error("同步线体 {} 数据时发生异常", line.getCode(), e);
			}
		}

		logger.info("所有线体当天数据同步完成，共同步 {} 条数据", totalSyncCount);
		return totalSyncCount;
	}

	@Override
	public LabelWorkProcessAnalysis saveOrUpdate(LabelWorkProcessAnalysis data) {
		if (data == null) {
			return null;
		}

		// 设置同步时间
		data.setSyncTime(new Date());

		// 检查是否已存在相同的记录
		Optional<LabelWorkProcessAnalysis> existingData = repository.findByLbIdAndPlId(data.getLbId(), data.getPlId());

		if (existingData.isPresent()) {
			// 更新现有记录
			LabelWorkProcessAnalysis existing = existingData.get();
			existing.setMo(data.getMo());
			existing.setFirstResult(data.getFirstResult());
			existing.setKeyWpFirstResult(data.getKeyWpFirstResult());
			existing.setFinalResult(data.getFinalResult());
			existing.setFinalWpCmpDate(data.getFinalWpCmpDate());
			existing.setSyncTime(data.getSyncTime());
			return repository.save(existing);
		}
		else {
			// 插入新记录
			return repository.save(data);
		}
	}

	@Override
	public int batchSaveOrUpdate(List<LabelWorkProcessAnalysis> dataList) {
		if (dataList == null || dataList.isEmpty()) {
			return 0;
		}

		int savedCount = 0;
		for (LabelWorkProcessAnalysis data : dataList) {
			try {
				saveOrUpdate(data);
				savedCount++;
			}
			catch (Exception e) {
				logger.error("保存标签工艺过程分析数据失败：{}", data, e);
			}
		}

		return savedCount;
	}

}
