package com.github.cret.web.oee.domain.feedback;

import java.util.Date;
import java.util.List;

import com.github.cret.web.oee.document.feedback.ResponseConfig;
import com.github.cret.web.oee.document.feedback.SendUser;

/**
 * 单个用户发送记录
 */
public class FeedbackTriggerSingleUserSendRecord {

	String triggerRecordId;

	Date expectedSendTime;

	List<SendUser> sendUser;

	ResponseConfig responseConfig;

	public String getTriggerRecordId() {
		return triggerRecordId;
	}

	public void setTriggerRecordId(String triggerRecordId) {
		this.triggerRecordId = triggerRecordId;
	}

	public Date getExpectedSendTime() {
		return expectedSendTime;
	}

	public void setExpectedSendTime(Date expectedSendTime) {
		this.expectedSendTime = expectedSendTime;
	}

	public List<SendUser> getSendUser() {
		return sendUser;
	}

	public void setSendUser(List<SendUser> sendUser) {
		this.sendUser = sendUser;
	}

	public ResponseConfig getResponseConfig() {
		return responseConfig;
	}

	public void setResponseConfig(ResponseConfig responseConfig) {
		this.responseConfig = responseConfig;
	}

}
