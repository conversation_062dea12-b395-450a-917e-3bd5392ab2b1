package com.github.cret.web.oee.repository;

import java.util.Date;
import java.util.List;
import java.util.Optional;

import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.stereotype.Repository;

import com.github.cret.web.oee.document.mes.LabelWorkProcessAnalysis;

@Repository
public interface LabelWorkProcessAnalysisRepository extends MongoRepository<LabelWorkProcessAnalysis, String> {

	/**
	 * 根据线体ID查询标签工艺过程分析数据
	 * @param plId 线体ID
	 * @return 标签工艺过程分析数据列表
	 */
	List<LabelWorkProcessAnalysis> findByPlId(String plId);

	/**
	 * 根据线体ID和时间范围查询标签工艺过程分析数据
	 * @param plId 线体ID
	 * @param startDate 开始时间
	 * @param endDate 结束时间
	 * @return 标签工艺过程分析数据列表
	 */
	List<LabelWorkProcessAnalysis> findByPlIdAndFinalWpCmpDateBetween(String plId, Date startDate, Date endDate);

	/**
	 * 根据标签ID和线体ID查找唯一记录
	 * @param lbId 标签ID
	 * @param plId 线体ID
	 * @return 标签工艺过程分析数据
	 */
	Optional<LabelWorkProcessAnalysis> findByLbIdAndPlId(String lbId, String plId);

	/**
	 * 根据线体ID和制造订单号查询
	 * @param plId 线体ID
	 * @param mo 制造订单号
	 * @return 标签工艺过程分析数据列表
	 */
	List<LabelWorkProcessAnalysis> findByPlIdAndMo(String plId, String mo);

	/**
	 * 删除指定线体和时间范围的数据
	 * @param plId 线体ID
	 * @param startDate 开始时间
	 * @param endDate 结束时间
	 */
	void deleteByPlIdAndFinalWpCmpDateBetween(String plId, Date startDate, Date endDate);

}
