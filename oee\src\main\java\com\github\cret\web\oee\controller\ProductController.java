package com.github.cret.web.oee.controller;

import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import com.alibaba.excel.EasyExcel;
import com.github.cret.web.common.domain.PageList;
import com.github.cret.web.common.domain.PageableParam;
import com.github.cret.web.oee.document.analyze.Product;
import com.github.cret.web.oee.service.ProductService;

import jakarta.servlet.http.HttpServletResponse;

@RestController
@RequestMapping("/product")
public class ProductController {

	@Autowired
	private ProductService productService;

	@PostMapping
	public Product create(@RequestBody Product product) {
		return productService.save(product);
	}

	@GetMapping("/{id}")
	public Product getById(@PathVariable String id) {
		return productService.findById(id);
	}

	@PostMapping("/page")
	public PageList<Product> page(@RequestBody PageableParam<Product> param) {
		return productService.page(param);
	}

	@GetMapping
	public List<Product> getAll() {
		return productService.findAll();
	}

	@PutMapping("/{id}")
	public Product update(@PathVariable String id, @RequestBody Product product) {
		product.setId(id);
		return productService.save(product);
	}

	@DeleteMapping("/{id}")
	public void delete(@PathVariable String id) {
		productService.deleteById(id);
	}

	@PostMapping("/import")
	public void importExcel(@RequestParam("file") MultipartFile file) throws IOException {
		productService.importExcel(file);
	}

	@PostMapping("/export")
	public void exportExcel(HttpServletResponse response, @RequestBody Product param) throws IOException {
		response.setContentType("application/vnd.ms-excel");
		response.setCharacterEncoding("utf-8");
		String fileName = URLEncoder.encode("产品信息", StandardCharsets.UTF_8);
		response.setHeader("Content-disposition", "attachment;filename=" + fileName + ".xlsx");

		List<Product> dataList = productService.findList(param);
		EasyExcel.write(response.getOutputStream(), Product.class).sheet("产品信息").doWrite(dataList);
	}

}
