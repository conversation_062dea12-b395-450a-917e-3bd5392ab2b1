package com.github.cret.web.oee.document;

import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

import com.github.cret.web.oee.enums.DeviceCategory;
import com.github.cret.web.oee.enums.DeviceTrack;
import com.github.cret.web.oee.enums.DeviceType;

/**
 * 设备
 */
@Document("t_device")
public class Device {

	@Id
	private String id;

	@Field(name = "name")
	private String name;

	@Field(name = "code")
	private String code;

	@Field(name = "type")
	private DeviceType type;

	@Field(name = "category")
	private DeviceCategory category;

	@Field(name = "track")
	private String track;

	@Field(name = "line_id")
	private String lineId;

	@Field(name = "client_ip")
	private String clientIp;

	@Field(name = "client_port")
	private String clientPort;

	@Field(name = "sort")
	private Integer sort;

	@Field(name = "group")
	private Integer group;

	// 0为禁用, 1为启用
	@Field(name = "enable")
	private Integer enable;

	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getCode() {
		return code;
	}

	public void setCode(String code) {
		this.code = code;
	}

	public DeviceType getType() {
		return type;
	}

	public void setType(DeviceType type) {
		this.type = type;
	}

	public DeviceCategory getCategory() {
		return category;
	}

	public void setCategory(DeviceCategory category) {
		this.category = category;
	}

	public String getTrack() {
		return track;
	}

	public void setTrack(String track) {
		this.track = track;
	}

	public String getLineId() {
		return lineId;
	}

	public void setLineId(String lineId) {
		this.lineId = lineId;
	}

	public String getClientIp() {
		return clientIp;
	}

	public void setClientIp(String clientIp) {
		this.clientIp = clientIp;
	}

	public String getClientPort() {
		return clientPort;
	}

	public void setClientPort(String clientPort) {
		this.clientPort = clientPort;
	}

	public Integer getSort() {
		return sort;
	}

	public void setSort(Integer sort) {
		this.sort = sort;
	}

	public Integer getGroup() {
		return group;
	}

	public void setGroup(Integer group) {
		this.group = group;
	}

	public Integer getEnable() {
		return enable;
	}

	public void setEnable(Integer enable) {
		this.enable = enable;
	}

	// 辅助方法：根据 code 获取 DeviceTrack 枚举
	public DeviceTrack getTrackEnum() {
		return DeviceTrack.getByCode(this.track);
	}

	// 辅助方法：设置 DeviceTrack 枚举并存储其 code
	public void setTrackEnum(DeviceTrack track) {
		this.track = track != null ? track.getCode() : null;
	}

}